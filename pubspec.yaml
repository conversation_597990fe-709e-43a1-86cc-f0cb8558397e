name: sphere
description: "Sphere application"
publish_to: "none"
version: 0.3.1+1

environment:
  sdk: ">=3.6.1 <4.0.0"

# --------------------- Dependencies ---------------------
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Core & State Management
  bloc: ^9.0.0
  flutter_bloc: ^9.0.0

  # Data Storage & Persistence
  flutter_secure_storage: ^9.2.2
  shared_preferences: ^2.3.3

  # Navigation
  auto_route: ^9.2.2

  # UI Components & Styling
  bitsdojo_window: ^0.1.6
  dotted_border: ^2.1.0
  dotted_line: ^3.2.3
  flutter_animate: ^4.5.0
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0
  syncfusion_flutter_pdfviewer: ^28.2.9

  # Asset Management
  flutter_gen: ^5.8.0
  flutter_svg: ^2.0.14

  # Network & API
  dio: ^5.7.0
  onesignal_flutter: ^5.3.0

  # Utilities & Helpers
  archive: ^4.0.2
  file_picker: ^8.1.4
  flutter_dotenv: ^5.2.1
  logger: ^2.5.0
  package_info_plus: ^8.1.2
  path: ^1.9.0
  url_launcher: ^6.3.1
  local_notifier: ^0.1.6
  tray_manager: ^0.3.2
  # workmanager: ^0.5.2
  window_manager: ^0.4.3

  # Serialization & Code Generation
  freezed_annotation: ^2.4.4
  json_annotation: ^4.8.1
  data_table_2: ^2.6.0
  intl: ^0.19.0
  cupertino_icons: ^1.0.8

# --------------------- Dev Dependencies ---------------------
dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Analysis
  flutter_lints: ^5.0.0

  # Code Generation
  auto_route_generator: ^9.0.0
  build_runner: ^2.4.11
  flutter_gen_runner: ^5.8.0
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  flutter_launcher_icons: ^0.14.3
  analyzer: ^6.3.0

# --------------------- Flutter Launcher Icons ---------------------

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/start.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/start.png"
    background_color: "#fff"
    theme_color: "#fff"
  windows:
    generate: true
    image_path: "assets/images/start.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/start.png"

# --------------------- Flutter Configuration ---------------------
flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/icons/
    - assets/images/
    - assets/fonts/
    - .env

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-VariableFont_opsz,wght.ttf
    - family: JetBrainsMono
      fonts:
        - asset: assets/fonts/JetBrainsMono.ttf

flutter_gen:
  output: lib/core/gen
