import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sphere/core/navigation/index.dart';
import 'package:sphere/core/settings.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/auth/presentation/bloc/bloc.dart';
import 'package:sphere/features/deliveries/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/arrows/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/structure/presentation/bloc/bloc.dart';
import 'package:sphere/shared/styles/themes.dart';

class MyApp extends StatelessWidget {
  const MyApp({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    CoreSettings().getSystemUIOverlaySettings(true);
    AppRouter appRouter = AppRouter();

    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => BlocInitial()),
        BlocProvider(create: (_) => BlocPurchaseList()),
        BlocProvider(create: (_) => BlocArrows()),
        BlocProvider(create: (_) => BlocStructure()),
        BlocProvider(create: (_) => BlocAuth()),
        BlocProvider(create: (_) => BlocDeliveries()),
      ],
      child: MaterialApp.router(
        restorationScopeId: 'app',
        scrollBehavior: CupertinoScrollBehavior(),

        debugShowCheckedModeBanner: false,

        // russian localization only
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ru', ''), // Russian, no country code
        ],

        // themes
        theme: AppThemes.lightTheme,
        darkTheme: AppThemes.darkTheme,
        themeMode: ThemeMode.light,

        routerConfig: appRouter.config(
          navigatorObservers: () => [AutoRouteObserver()],
        ),
      ),
    );
  }
}
