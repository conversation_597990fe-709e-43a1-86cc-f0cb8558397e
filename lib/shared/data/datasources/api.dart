import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';
import 'package:sphere/core/helpers/context_service.dart';
import 'package:sphere/shared/widgets/utility/snackbar.dart';

class API {
  static final dio = Dio()
    ..options.validateStatus = (status) {
      // Позволяем Dio не выбрасывать исключение для всех статусов от 200 до 500
      return status != null && status >= 200 && status < 500;
    };

  // development / production
  static final mode = dotenv.get('MODE');

  static final developmentURL = dotenv.get('API_URL_DEVELOPMENT');
  static final productuionURL = dotenv.get('API_URL_PRODUCTION');

  static final postfix = dotenv.get('API_POSTFIX');

  static final secretAPI = dotenv.get('SECRET_API_KEY');

  // получение базового url
  static String getBaseURL() {
    if (mode == 'development') {
      return '$developmentURL$postfix';
    } else {
      return '$productuionURL$postfix';
    }
  }

  // обработка запросов
  static Future<Response<T>> request<T>({
    String url = '/',
    Object body = const {},
    String method = 'GET',
    String authorization = '',
    T Function(Map<String, dynamic>)? fromJson,
    Options? options,
    BuildContext? context,
  }) async {
    final logger = Logger(
      printer: PrettyPrinter(),
    );
    try {
      // final prefs = await SharedPreferences.getInstance();
      const prefs = FlutterSecureStorage();
      final accessToken = await prefs.read(key: 'accessToken') ?? authorization;

      // try request
      logger.i('--- INPUT ---');
      logger.d(
          'URL: ${getBaseURL() + url} \nSECRET_API: $secretAPI\nBODY: ${body.toString()}\nACCESS_TOKEN: $accessToken');
      final result = await dio.request(
        '${getBaseURL()}$url',
        data: body,
        options: Options(
          method: options?.method ?? method,
          contentType: options?.contentType ?? 'application/json',
          headers: options?.headers ??
              {
                'Authorization': 'Bearer $accessToken',
              },
        ),
      );

      // cast and output response
      logger.i('--- OUTPUT ---');
      logger.d(result.data);

      final globalContext = NavigationService.navigatorKey.currentContext;

      if (context != null || globalContext != null) {
        if (result.data['message'] != null) {
          ScaffoldMessenger.of(context ?? globalContext!).showSnackBar(
            CustomSnackbar(
              text: '${result.statusCode}: ${result.data['message']}',
              type: (result.statusCode ?? 0) >= 400
                  ? SnackBarType.error
                  : SnackBarType.common,
            ).toSnackBar(context ?? globalContext!),
          );
        }
      }

      return Response(
        data: fromJson?.call(result.data) ?? result.data,
        requestOptions: result.requestOptions,
        statusCode: result.statusCode,
        statusMessage: result.statusMessage,
      );
    } catch (e) {
      // catch if error
      logger.e(e);
      return Response(
        requestOptions: RequestOptions(),
        statusCode: 500,
        statusMessage: e.toString(),
      );
    }
  }
}
