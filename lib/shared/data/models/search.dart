import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/project/data/models/statuses.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';
import 'package:sphere/features/user/data/models/role.dart';

part 'search.freezed.dart';
part 'search.g.dart';

// --- search ---
@freezed
class SearchModel with _$SearchModel {
  @JsonSerializable(includeIfNull: false)
  const factory SearchModel({
    SearchFiltersModel? filters,
    SearchPaginationModel? pagination,
    SearchSortModel? sort,
  }) = _SearchModel;

  factory SearchModel.fromJson(Map<String, dynamic> json) =>
      _$SearchModelFromJson(json);
}

// --- filters ---
@freezed
class SearchFiltersModel with _$SearchFiltersModel {
  @JsonSerializable(includeIfNull: false)
  const factory SearchFiltersModel({
    String? query,
    String? branchId,
    Department? department,
    DocumentDepartment? documentDepartment,
    // project
    List<String>? projectIds,
    ProjectStatus? status,
    String? userId,
    // product
    List<String>? productIds,
    bool? root,
    ProductType? type,
    String? parentProductId,
    // user
    List<UserRole>? roles,
    String? projectId,
    String? productId,
    // documents
    bool? bluePrint,
    bool? isFolder,
    String? extension,
    String? parentFolderId,
    int? version,
    // nomenclatures / storage
    String? materialId,
    StorageType? storageType,
    NomenclatureType? materialType,
    UnitType? baseUnit,
    bool? visible,
    bool? remainingMaterials,
    // tasks
    String? workerId,
    TaskStatus? taskStatus,
    DateTime? fromDate,
    DateTime? toDate,
    // clients
    ClientType? clientType,
    // deliveries
    String? provisionId,
    List<DeliveryStatus>? deliveryStatus,
    // provisions
    ProvisionsFilter? filterType,
    // Fields from JSON
    @JsonKey(name: 'dateFrom') DateTime? dateFrom,
    @JsonKey(name: 'dateTo') DateTime? dateTo,
    String? drawingNumber,
    List<String>? drawingNumbers,
    String? name,
    List<String>? names,
    String? material,
    List<String>? materials,
    ParametersFeatureType? feature,
    List<ParametersFeatureType>? features,
    double? massFrom,
    double? massTo,
    int? quantityFrom,
    int? quantityTo,
    double? totalMassFrom,
    double? totalMassTo,
    String? requirement,
    List<String>? requirements,
    String? materialRequirement,
    List<String>? materialRequirements,
    int? priority,
    List<int>? priorities,
    String? responsiblePerson,
    List<String>? responsiblePersons,
    @JsonKey(name: 'taskDateFrom') DateTime? taskDateFrom,
    @JsonKey(name: 'taskDateTo') DateTime? taskDateTo,
    List<SupplyStatus>? supplyStatus,
    @JsonKey(name: 'plannedContractDateFrom') DateTime? plannedContractDateFrom,
    @JsonKey(name: 'plannedContractDateTo') DateTime? plannedContractDateTo,
    List<int>? lotNumbers, // Changed to List<int> to match JSON
    List<String>? lotNames,
    String? contractNumber,
    List<String>? contractNumbers,
    String? supplier,
    List<String>? suppliers,
    @JsonKey(name: 'deliveryDateFrom') DateTime? deliveryDateFrom,
    @JsonKey(name: 'deliveryDateTo') DateTime? deliveryDateTo,
    double? costFrom,
    double? costTo,
    List<String>? lotIds,
    List<String>? contractIds,
    String? parentName,
    List<String>? parentNames,
  }) = _SearchFiltersModel;

  factory SearchFiltersModel.fromJson(Map<String, dynamic> json) =>
      _$SearchFiltersModelFromJson(json);
}

// --- pagination ---
@freezed
class SearchPaginationModel with _$SearchPaginationModel {
  @JsonSerializable(includeIfNull: false)
  const factory SearchPaginationModel({
    int? offset,
    int? limit,
  }) = _SearchPaginationModel;

  factory SearchPaginationModel.fromJson(Map<String, dynamic> json) =>
      _$SearchPaginationModelFromJson(json);
}

// --- sort ---
@freezed
class SearchSortModel with _$SearchSortModel {
  @JsonSerializable(includeIfNull: false)
  const factory SearchSortModel({
    String? field,
    String? order,
  }) = _SearchSortModel;

  factory SearchSortModel.fromJson(Map<String, dynamic> json) =>
      _$SearchSortModelFromJson(json);
}

// --- Extension for SearchFiltersModel ---
extension SearchFiltersModelX on SearchFiltersModel {
  /// Counts the number of active filters (excluding required ones)
  int get activeFiltersCount {
    int count = 0;

    // Basic filters
    if (query != null && query!.isNotEmpty) count++;
    if (branchId != null && branchId!.isNotEmpty) count++;
    if (department != null) count++;
    if (documentDepartment != null) count++;
    if (projectIds != null && projectIds!.isNotEmpty) count++;
    if (status != null) count++;
    if (userId != null && userId!.isNotEmpty) count++;
    if (productIds != null && productIds!.isNotEmpty) count++;
    if (root != null) count++;
    if (type != null) count++;
    if (parentProductId != null && parentProductId!.isNotEmpty) count++;
    if (roles != null && roles!.isNotEmpty) count++;
    if (productId != null && productId!.isNotEmpty) count++;
    if (bluePrint != null) count++;
    if (isFolder != null) count++;
    if (extension != null && extension!.isNotEmpty) count++;
    if (parentFolderId != null && parentFolderId!.isNotEmpty) count++;
    if (version != null) count++;
    if (materialId != null && materialId!.isNotEmpty) count++;
    if (storageType != null) count++;
    if (materialType != null) count++;
    if (baseUnit != null) count++;
    if (visible != null) count++;
    if (remainingMaterials != null) count++;
    if (workerId != null && workerId!.isNotEmpty) count++;
    if (taskStatus != null) count++;
    if (fromDate != null) count++;
    if (toDate != null) count++;
    if (clientType != null) count++;
    if (provisionId != null && provisionId!.isNotEmpty) count++;
    if (deliveryStatus != null && deliveryStatus!.isNotEmpty) count++;
    if (filterType != null) count++;

    // New fields from JSON
    if (dateFrom != null) count++;
    if (dateTo != null) count++;
    if (drawingNumber != null && drawingNumber!.isNotEmpty) count++;
    if (name != null && name!.isNotEmpty) count++;
    if (material != null && material!.isNotEmpty) count++;
    if (feature != null) count++;
    if (massFrom != null) count++;
    if (massTo != null) count++;
    if (quantityFrom != null) count++;
    if (quantityTo != null) count++;
    if (requirements != null && requirements!.isNotEmpty) count++;
    if (materialRequirements != null && materialRequirements!.isNotEmpty)
      count++;
    if (priority != null) count++;
    if (responsiblePerson != null && responsiblePerson!.isNotEmpty) count++;
    if (taskDateFrom != null) count++;
    if (taskDateTo != null) count++;
    if (supplyStatus != null && supplyStatus!.isNotEmpty) count++;
    if (plannedContractDateFrom != null) count++;
    if (plannedContractDateTo != null) count++;
    if (lotNumbers != null && lotNumbers!.isNotEmpty) count++;
    if (lotNames != null && lotNames!.isNotEmpty) count++;
    if (contractNumber != null && contractNumber!.isNotEmpty) count++;
    if (supplier != null && supplier!.isNotEmpty) count++;
    if (deliveryDateFrom != null) count++;
    if (deliveryDateTo != null) count++;
    if (costFrom != null) count++;
    if (costTo != null) count++;
    if (lotIds != null && lotIds!.isNotEmpty) count++;
    if (contractIds != null && contractIds!.isNotEmpty) count++;

    return count;
  }

  /// Returns a map of active filter field names to their display names
  Map<String, String> get activeFilterNames {
    final Map<String, String> names = {};

    if (query != null && query!.isNotEmpty) names['query'] = 'Поисковой запрос';
    if (branchId != null && branchId!.isNotEmpty)
      names['branchId'] = 'ИД филиала';
    if (department != null) names['department'] = 'Отдел';
    if (documentDepartment != null)
      names['documentDepartment'] = 'Отдел документов';
    if (projectIds != null && projectIds!.isNotEmpty)
      names['projectIds'] = 'ИД проектов';
    if (status != null) names['status'] = 'Статус проекта';
    if (userId != null && userId!.isNotEmpty)
      names['userId'] = 'ИД пользователя';
    if (productIds != null && productIds!.isNotEmpty)
      names['productIds'] = 'ИД продуктов';
    if (root != null) names['root'] = 'Корневой продукт';
    if (type != null) names['type'] = 'Тип продукта';
    if (parentProductId != null && parentProductId!.isNotEmpty)
      names['parentProductId'] = 'ИД родительского продукта';
    if (roles != null && roles!.isNotEmpty) names['roles'] = 'Роли';
    if (productId != null && productId!.isNotEmpty)
      names['productId'] = 'ИД продукта';
    if (bluePrint != null) names['bluePrint'] = 'Чертеж';
    if (isFolder != null) names['isFolder'] = 'Папка';
    if (extension != null && extension!.isNotEmpty)
      names['extension'] = 'Расширение';
    if (parentFolderId != null && parentFolderId!.isNotEmpty)
      names['parentFolderId'] = 'ИД родительской папки';
    if (version != null) names['version'] = 'Версия';
    if (materialId != null && materialId!.isNotEmpty)
      names['materialId'] = 'ИД материала';
    if (storageType != null) names['storageType'] = 'Тип хранения';
    if (materialType != null) names['materialType'] = 'Тип материала';
    if (baseUnit != null) names['baseUnit'] = 'Базовая единица';
    if (visible != null) names['visible'] = 'Видимость';
    if (remainingMaterials != null)
      names['remainingMaterials'] = 'Оставшиеся материалы';
    if (workerId != null && workerId!.isNotEmpty)
      names['workerId'] = 'ИД работника';
    if (taskStatus != null) names['taskStatus'] = 'Статус задачи';
    if (fromDate != null || toDate != null) names['fromDate'] = 'Дата';
    if (clientType != null) names['clientType'] = 'Тип клиента';
    if (provisionId != null && provisionId!.isNotEmpty)
      names['provisionId'] = 'ИД поставки';
    if (deliveryStatus != null && deliveryStatus!.isNotEmpty)
      names['deliveryStatus'] = 'Статус доставки';
    // if (filterType != null) names['filterType'] = filterType!.getName();
    if (dateFrom != null || dateTo != null) names['dateFrom'] = 'Дата ввода';
    if (drawingNumber != null && drawingNumber!.isNotEmpty)
      names['drawingNumber'] = 'Номер чертежа';
    if (name != null && name!.isNotEmpty) names['name'] = 'Название: $name';
    if (material != null && material!.isNotEmpty)
      names['material'] = 'Материал: $material';
    if (feature != null) names['feature'] = 'Признак: ${feature!.getName()}';
    if (massFrom != null || massTo != null)
      names['massFrom'] = 'Масса: $massFrom – $massTo';
    if (quantityFrom != null || quantityTo != null)
      names['quantityFrom'] = 'Количество $quantityFrom – $quantityTo';
    if (requirements != null && requirements!.isNotEmpty)
      names['requirements'] = 'Примечание';
    if (materialRequirements != null && materialRequirements!.isNotEmpty)
      names['materialRequirements'] = 'Доп. требования';
    if (priority != null) names['priority'] = 'Приоритет: $priority';
    if (responsiblePerson != null && responsiblePerson!.isNotEmpty)
      names['responsiblePerson'] = 'Ответственный';
    if (taskDateFrom != null || taskDateTo != null)
      names['taskDateFrom'] = 'Дата задачи';
    if (supplyStatus != null && supplyStatus!.isNotEmpty)
      names['supplyStatus'] = 'Статус закупа';
    if (plannedContractDateFrom != null || plannedContractDateTo != null)
      names['plannedContractDateFrom'] = 'Дата контракта';
    if (lotNumbers != null && lotNumbers!.isNotEmpty)
      names['lotNumbers'] = 'Номер лота';
    if (lotNames != null && lotNames!.isNotEmpty)
      names['lotNames'] = 'Название лота';
    if (contractNumber != null && contractNumber!.isNotEmpty)
      names['contractNumber'] = 'Номер заказа';
    if (supplier != null && supplier!.isNotEmpty)
      names['supplier'] = 'Поставщик';
    if (deliveryDateFrom != null || deliveryDateTo != null)
      names['deliveryDateFrom'] = 'Дата поставки';
    if (costFrom != null || costTo != null) names['costFrom'] = 'Стоимость';
    if (lotIds != null && lotIds!.isNotEmpty) names['lotIds'] = 'ИД лотов';
    if (contractIds != null && contractIds!.isNotEmpty)
      names['contractIds'] = 'ИД контрактов';

    return names;
  }

  /// Checks if there are any active filters
  bool get hasActiveFilters => activeFiltersCount > 0;

  /// Creates a copy with cleared filters (except required projectId)
  SearchFiltersModel clearFilters() {
    return SearchFiltersModel(projectId: projectId);
  }
}
