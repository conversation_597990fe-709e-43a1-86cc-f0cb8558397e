import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class DescriptionText extends StatelessWidget {
  const DescriptionText({
    super.key,
    required this.text,
    this.style,
    this.textAlign,
  });

  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkTheme
        ? AppColors.darkPrimary.withOpacity(0.33)
        : AppColors.lightPrimary.withOpacity(0.33);

    return Text(
      text,
      textAlign: textAlign,
      style: Fonts.bodySmall.merge(TextStyle(color: textColor)).merge(style),
    );
  }
}
