import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class CustomDrawer {
  static final CustomDrawer instance = CustomDrawer._internal();

  CustomDrawer._internal();

  factory CustomDrawer() => instance;

  OverlayEntry? _overlayEntry;
  // BuildContext? _context;
  AnimationController? _animationController;

  // void initialize(BuildContext context) {
  //   _context = context;
  // }

  void show({
    required BuildContext context,
    required TickerProvider vsync,
    required Widget child,
    bool alignRight = true,
  }) {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: vsync,
    );

    _overlayEntry = _createOverlayEntry(
      context: context,
      child: child,
      alignRight: alignRight,
    );
    Overlay.of(context).insert(_overlayEntry!);

    _animationController!.forward();
  }

  void hide() {
    if (_animationController != null) {
      _animationController!.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
        _animationController?.dispose();
        _animationController = null;
      });
    } else {
      _overlayEntry?.remove();
      _overlayEntry = null;
    }
  }

  OverlayEntry _createOverlayEntry({
    required BuildContext context,
    required Widget child,
    required bool alignRight,
  }) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    final animation = CurvedAnimation(
      parent: _animationController!,
      curve: Curves.easeInOut,
    );

    return OverlayEntry(
      builder: (context) {
        final screenSize = MediaQuery.of(context).size;
        final menuWidth =
            screenSize.width < 1000 ? screenSize.width : screenSize.width / 2;

        return AnimatedBuilder(
          animation: animation,
          builder: (context, _) {
            final opacity = animation.value;
            final offsetX = -menuWidth * (1 - animation.value);

            return Stack(
              fit: StackFit.expand,
              children: [
                GestureDetector(
                  onTap: hide,
                  onSecondaryTap: hide,
                  child: Container(
                    color: Colors.black.withOpacity(0.2 * opacity),
                  ),
                ),
                Positioned(
                  left: alignRight ? null : offsetX,
                  right: alignRight ? offsetX : null,
                  top: 0,
                  bottom: 0,
                  child: Material(
                    elevation: 4,
                    clipBehavior: Clip.hardEdge,
                    shape: BorderDirectional(
                      start: BorderSide(
                        color: isDarkTheme
                            ? AppColors.darkStroke
                            : AppColors.lightStroke,
                        width: 1.0,
                      ),
                    ),
                    shadowColor: AppColors.lightPrimary.withOpacity(0.1),
                    color: isDarkTheme
                        ? AppColors.darkBackground
                        : AppColors.lightBackground,
                    child: SizedBox(
                      width: menuWidth,
                      child: SafeArea(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (screenSize.width < 1000)
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 20,
                                  top: 12.0,
                                ),
                                child: IconButton(
                                  onPressed: hide,
                                  icon: SVG(Assets.icons.arrowBack),
                                ),
                              ),
                            Expanded(child: child),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
