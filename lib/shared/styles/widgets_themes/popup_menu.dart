import 'package:flutter/material.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';

class CustomPopupMenuThemeData {
  static final PopupMenuThemeData lightTheme = PopupMenuThemeData(
    color: AppColors.lightBackground,
    shape: RoundedRectangleBorder(
      side: const BorderSide(color: AppColors.lightStroke),
      borderRadius: BorderRadius.circular(20.0),
    ),
    position: PopupMenuPosition.under,
    textStyle: Fonts.labelMedium,
    shadowColor: AppColors.lightPrimary.withOpacity(0.1),
  );

  static final PopupMenuThemeData darkTheme = PopupMenuThemeData(
    color: AppColors.darkBackground,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(20.0),
      side: const BorderSide(color: AppColors.darkStroke),
    ),
    position: PopupMenuPosition.under,
    textStyle: Fonts.labelMedium,
    shadowColor: AppColors.lightPrimary.withOpacity(0.1),
  );
}
