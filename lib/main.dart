import 'dart:io';

import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:local_notifier/local_notifier.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:sphere/core/helpers/http_overrides.dart';

import 'app.dart';

void main() async {
  // load .env file
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: '.env');

  HttpOverrides.global = MyHttpOverrides();

  // One signal (notifications)
  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  OneSignal.initialize("************************************");
  OneSignal.Notifications.requestPermission(true);

  runApp(const MyApp());

  if (!kIsWeb && (Platform.isWindows || Platform.isLinux || Platform.isMacOS)) {
    await localNotifier.setup(
      appName: 'Сфера — Система управления проектами',
      shortcutPolicy: ShortcutPolicy.requireCreate,
    );

    doWhenWindowReady(() {
      const initialSize = Size(900, 600);
      appWindow.minSize = initialSize;
      appWindow.size = initialSize;
      appWindow.alignment = Alignment.center;
      appWindow.title = 'Сфера';
      appWindow.show();
      appWindow.maximize();
    });
  }
}
