// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GetRequirementsOutputImpl _$$GetRequirementsOutputImplFromJson(
        Map<String, dynamic> json) =>
    _$GetRequirementsOutputImpl(
      items: (json['items'] as List<dynamic>?)
          ?.map(
              (e) => ProvisionProductModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$GetRequirementsOutputImplToJson(
        _$GetRequirementsOutputImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_$ProvisionsCreateModelImpl _$$ProvisionsCreateModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionsCreateModelImpl(
      provisionName: json['provisionName'] as String?,
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
      items: (json['items'] as List<dynamic>?)
          ?.map((e) =>
              ProvisionsCreateItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      deliveryGroups: (json['deliveryGroups'] as List<dynamic>?)
          ?.map((e) => DeliveryGroupModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProvisionsCreateModelImplToJson(
        _$ProvisionsCreateModelImpl instance) =>
    <String, dynamic>{
      if (instance.provisionName case final value?) 'provisionName': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'filterType': value,
      if (instance.items case final value?) 'items': value,
      if (instance.deliveryGroups case final value?) 'deliveryGroups': value,
    };

const _$ProvisionsFilterEnumMap = {
  ProvisionsFilter.cooperation: 'cooperation',
  ProvisionsFilter.materials: 'materials',
  ProvisionsFilter.assemblyMaterials: 'assembly_materials',
  ProvisionsFilter.purchased: 'purchased',
  ProvisionsFilter.assembly: 'assembly',
};

_$ProvisionsCreateItemModelImpl _$$ProvisionsCreateItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionsCreateItemModelImpl(
      productId: json['productId'] as String?,
      featureType: $enumDecodeNullable(
          _$ParametersFeatureTypeEnumMap, json['featureType']),
      quantity: (json['quantity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ProvisionsCreateItemModelImplToJson(
        _$ProvisionsCreateItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (_$ParametersFeatureTypeEnumMap[instance.featureType]
          case final value?)
        'featureType': value,
      if (instance.quantity case final value?) 'quantity': value,
    };

const _$ParametersFeatureTypeEnumMap = {
  ParametersFeatureType.mechanics: 'MECHANICS',
  ParametersFeatureType.bending: 'BENDING',
  ParametersFeatureType.cutting: 'CUTTING',
  ParametersFeatureType.coating: 'COATING',
  ParametersFeatureType.heatTreatment: 'HEAT_TREATMENT',
  ParametersFeatureType.rolling: 'ROLLING',
  ParametersFeatureType.welding: 'WELDING',
};

_$ProvisionsGetMaterialsImpl _$$ProvisionsGetMaterialsImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionsGetMaterialsImpl(
      filter: $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filter']),
    );

Map<String, dynamic> _$$ProvisionsGetMaterialsImplToJson(
        _$ProvisionsGetMaterialsImpl instance) =>
    <String, dynamic>{
      if (_$ProvisionsFilterEnumMap[instance.filter] case final value?)
        'filter': value,
    };

_$ProvisionsColumnOptionsInputImpl _$$ProvisionsColumnOptionsInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionsColumnOptionsInputImpl(
      projectId: json['projectId'] as String?,
      column: $enumDecodeNullable(_$ProvisionColumnEnumMap, json['column']),
      search: json['search'] as String?,
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
    );

Map<String, dynamic> _$$ProvisionsColumnOptionsInputImplToJson(
        _$ProvisionsColumnOptionsInputImpl instance) =>
    <String, dynamic>{
      if (instance.projectId case final value?) 'projectId': value,
      if (_$ProvisionColumnEnumMap[instance.column] case final value?)
        'column': value,
      if (instance.search case final value?) 'search': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'filterType': value,
    };

const _$ProvisionColumnEnumMap = {
  ProvisionColumn.drawingNumber: 'drawingNumber',
  ProvisionColumn.name: 'name',
  ProvisionColumn.parentName: 'parentName',
  ProvisionColumn.material: 'material',
  ProvisionColumn.feature: 'feature',
  ProvisionColumn.requirements: 'requirements',
  ProvisionColumn.materialRequirements: 'materialRequirements',
  ProvisionColumn.priority: 'priority',
  ProvisionColumn.contractNumber: 'contractNumber',
  ProvisionColumn.supplier: 'supplier',
  ProvisionColumn.lotNumber: 'lotNumber',
  ProvisionColumn.lotName: 'lotName',
};

_$ProvisionsColumnOptionsOutputImpl
    _$$ProvisionsColumnOptionsOutputImplFromJson(Map<String, dynamic> json) =>
        _$ProvisionsColumnOptionsOutputImpl(
          items: (json['items'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
          totalItems: (json['totalItems'] as num?)?.toInt(),
        );

Map<String, dynamic> _$$ProvisionsColumnOptionsOutputImplToJson(
        _$ProvisionsColumnOptionsOutputImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_$ProvisionCreateLotInputImpl _$$ProvisionCreateLotInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionCreateLotInputImpl(
      projectId: json['projectId'] as String?,
      lotName: json['lotName'] as String?,
      productIds: (json['productIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      plannedTenderCompletionDate:
          json['plannedTenderCompletionDate'] as String?,
    );

Map<String, dynamic> _$$ProvisionCreateLotInputImplToJson(
        _$ProvisionCreateLotInputImpl instance) =>
    <String, dynamic>{
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.lotName case final value?) 'lotName': value,
      if (instance.productIds case final value?) 'productIds': value,
      if (instance.plannedTenderCompletionDate case final value?)
        'plannedTenderCompletionDate': value,
    };

_$ProvisionAddToLotInputImpl _$$ProvisionAddToLotInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionAddToLotInputImpl(
      lotNumbers: (json['lotNumbers'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      productIds: (json['productIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$ProvisionAddToLotInputImplToJson(
        _$ProvisionAddToLotInputImpl instance) =>
    <String, dynamic>{
      if (instance.lotNumbers case final value?) 'lotNumbers': value,
      if (instance.productIds case final value?) 'productIds': value,
    };

_$ProvisionCreateContractInputImpl _$$ProvisionCreateContractInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionCreateContractInputImpl(
      productIds: (json['productIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      contractDate: json['contractDate'] as String,
      contractStartDate: json['contractStartDate'] as String?,
      contractEndDate: json['contractEndDate'] as String?,
      supplierId: json['supplierId'] as String?,
      plannedDeliveryDate: json['plannedDeliveryDate'] as String?,
      contractPrice: (json['contractPrice'] as num).toDouble(),
      productDetails: (json['productDetails'] as List<dynamic>?)
          ?.map((e) => ProductDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      paymentDetails: (json['paymentDetails'] as List<dynamic>?)
          ?.map((e) => PaymentDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$ProvisionCreateContractInputImplToJson(
        _$ProvisionCreateContractInputImpl instance) =>
    <String, dynamic>{
      'productIds': instance.productIds,
      'contractDate': instance.contractDate,
      if (instance.contractStartDate case final value?)
        'contractStartDate': value,
      if (instance.contractEndDate case final value?) 'contractEndDate': value,
      if (instance.supplierId case final value?) 'supplierId': value,
      if (instance.plannedDeliveryDate case final value?)
        'plannedDeliveryDate': value,
      'contractPrice': instance.contractPrice,
      if (instance.productDetails case final value?) 'productDetails': value,
      if (instance.paymentDetails case final value?) 'paymentDetails': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'filterType': value,
      if (instance.notes case final value?) 'notes': value,
    };

_$ProductDetailImpl _$$ProductDetailImplFromJson(Map<String, dynamic> json) =>
    _$ProductDetailImpl(
      productId: json['productId'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      unit: json['unit'] as String?,
      distributions: (json['distributions'] as List<dynamic>?)
          ?.map((e) => Distribution.fromJson(e as Map<String, dynamic>))
          .toList(),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$ProductDetailImplToJson(_$ProductDetailImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.price case final value?) 'price': value,
      if (instance.unit case final value?) 'unit': value,
      if (instance.distributions case final value?) 'distributions': value,
      if (instance.notes case final value?) 'notes': value,
    };

_$DistributionImpl _$$DistributionImplFromJson(Map<String, dynamic> json) =>
    _$DistributionImpl(
      warehouseId: json['warehouseId'] as String?,
      quantity: (json['quantity'] as num?)?.toDouble(),
      deliveryDate: json['deliveryDate'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$DistributionImplToJson(_$DistributionImpl instance) =>
    <String, dynamic>{
      if (instance.warehouseId case final value?) 'warehouseId': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.deliveryDate case final value?) 'deliveryDate': value,
      if (instance.notes case final value?) 'notes': value,
    };

_$PaymentDetailImpl _$$PaymentDetailImplFromJson(Map<String, dynamic> json) =>
    _$PaymentDetailImpl(
      paymentDate: json['paymentDate'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$PaymentDetailImplToJson(_$PaymentDetailImpl instance) =>
    <String, dynamic>{
      if (instance.paymentDate case final value?) 'paymentDate': value,
      if (instance.amount case final value?) 'amount': value,
    };

_$ProvisionUpdateContractInputImpl _$$ProvisionUpdateContractInputImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionUpdateContractInputImpl(
      contractId: json['contractId'] as String,
      productIds: (json['productIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      contractDate: json['contractDate'] as String,
      contractStartDate: json['contractStartDate'] as String?,
      contractEndDate: json['contractEndDate'] as String?,
      supplierId: json['supplierId'] as String?,
      plannedDeliveryDate: json['plannedDeliveryDate'] as String?,
      contractPrice: (json['contractPrice'] as num).toDouble(),
      productDetails: (json['productDetails'] as List<dynamic>?)
          ?.map((e) => ProductDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      paymentDetails: (json['paymentDetails'] as List<dynamic>?)
          ?.map((e) => PaymentDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$$ProvisionUpdateContractInputImplToJson(
        _$ProvisionUpdateContractInputImpl instance) =>
    <String, dynamic>{
      'contractId': instance.contractId,
      'productIds': instance.productIds,
      'contractDate': instance.contractDate,
      if (instance.contractStartDate case final value?)
        'contractStartDate': value,
      if (instance.contractEndDate case final value?) 'contractEndDate': value,
      if (instance.supplierId case final value?) 'supplierId': value,
      if (instance.plannedDeliveryDate case final value?)
        'plannedDeliveryDate': value,
      'contractPrice': instance.contractPrice,
      if (instance.productDetails case final value?) 'productDetails': value,
      if (instance.paymentDetails case final value?) 'paymentDetails': value,
      if (_$ProvisionsFilterEnumMap[instance.filterType] case final value?)
        'filterType': value,
      if (instance.notes case final value?) 'notes': value,
    };

_$ProvisionChangeContractStatusInputImpl
    _$$ProvisionChangeContractStatusInputImplFromJson(
            Map<String, dynamic> json) =>
        _$ProvisionChangeContractStatusInputImpl(
          contractId: json['contractId'] as String?,
          action: $enumDecodeNullable(_$ContractActionEnumMap, json['action']),
          reason: json['reason'] as String?,
        );

Map<String, dynamic> _$$ProvisionChangeContractStatusInputImplToJson(
        _$ProvisionChangeContractStatusInputImpl instance) =>
    <String, dynamic>{
      if (instance.contractId case final value?) 'contractId': value,
      if (_$ContractActionEnumMap[instance.action] case final value?)
        'action': value,
      if (instance.reason case final value?) 'reason': value,
    };

const _$ContractActionEnumMap = {
  ContractAction.confirm: 'confirm',
  ContractAction.revert: 'revert',
  ContractAction.cancel: 'cancel',
};

_$WarehouseImpl _$$WarehouseImplFromJson(Map<String, dynamic> json) =>
    _$WarehouseImpl(
      id: json['id'] as String?,
      name: json['name'] as String,
      type: $enumDecode(_$WarehouseTypeEnumMap, json['type']),
      address: json['address'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$WarehouseImplToJson(_$WarehouseImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      'name': instance.name,
      'type': _$WarehouseTypeEnumMap[instance.type]!,
      if (instance.address case final value?) 'address': value,
      if (instance.description case final value?) 'description': value,
    };

const _$WarehouseTypeEnumMap = {
  WarehouseType.general: 'general',
  WarehouseType.cold: 'cold',
  WarehouseType.hazardous: 'hazardous',
};

_$WarehouseCreateInputImpl _$$WarehouseCreateInputImplFromJson(
        Map<String, dynamic> json) =>
    _$WarehouseCreateInputImpl(
      name: json['name'] as String,
      type: $enumDecode(_$WarehouseTypeEnumMap, json['type']),
      address: json['address'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$WarehouseCreateInputImplToJson(
        _$WarehouseCreateInputImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': _$WarehouseTypeEnumMap[instance.type]!,
      if (instance.address case final value?) 'address': value,
      if (instance.description case final value?) 'description': value,
    };

_$WarehouseUpdateInputImpl _$$WarehouseUpdateInputImplFromJson(
        Map<String, dynamic> json) =>
    _$WarehouseUpdateInputImpl(
      warehouseId: json['warehouseId'] as String,
      name: json['name'] as String?,
      address: json['address'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$WarehouseUpdateInputImplToJson(
        _$WarehouseUpdateInputImpl instance) =>
    <String, dynamic>{
      'warehouseId': instance.warehouseId,
      if (instance.name case final value?) 'name': value,
      if (instance.address case final value?) 'address': value,
      if (instance.description case final value?) 'description': value,
    };

_$WarehouseDeleteInputImpl _$$WarehouseDeleteInputImplFromJson(
        Map<String, dynamic> json) =>
    _$WarehouseDeleteInputImpl(
      warehouseId: json['warehouseId'] as String,
    );

Map<String, dynamic> _$$WarehouseDeleteInputImplToJson(
        _$WarehouseDeleteInputImpl instance) =>
    <String, dynamic>{
      'warehouseId': instance.warehouseId,
    };

_$WarehouseViewInputImpl _$$WarehouseViewInputImplFromJson(
        Map<String, dynamic> json) =>
    _$WarehouseViewInputImpl(
      warehouseId: json['warehouseId'] as String,
    );

Map<String, dynamic> _$$WarehouseViewInputImplToJson(
        _$WarehouseViewInputImpl instance) =>
    <String, dynamic>{
      'warehouseId': instance.warehouseId,
    };
