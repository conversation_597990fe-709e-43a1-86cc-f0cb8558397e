// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provision_search_filters.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProvisionSearchFiltersImpl _$$ProvisionSearchFiltersImplFromJson(
        Map<String, dynamic> json) =>
    _$ProvisionSearchFiltersImpl(
      dateFrom: json['dateFrom'] == null
          ? null
          : DateTime.parse(json['dateFrom'] as String),
      dateTo: json['dateTo'] == null
          ? null
          : DateTime.parse(json['dateTo'] as String),
      drawingNumber: json['drawingNumber'] as String?,
      name: json['name'] as String?,
      material: json['material'] as String?,
      feature: json['feature'] as String?,
      filterType:
          $enumDecodeNullable(_$ProvisionsFilterEnumMap, json['filterType']),
      massFrom: (json['massFrom'] as num?)?.toDouble(),
      massTo: (json['massTo'] as num?)?.toDouble(),
      quantityFrom: (json['quantityFrom'] as num?)?.toInt(),
      quantityTo: (json['quantityTo'] as num?)?.toInt(),
      requirements: json['requirements'] as String?,
      materialRequirements: json['materialRequirements'] as String?,
      priority: (json['priority'] as num?)?.toInt(),
      responsiblePerson: json['responsiblePerson'] as String?,
      taskDateFrom: json['taskDateFrom'] == null
          ? null
          : DateTime.parse(json['taskDateFrom'] as String),
      taskDateTo: json['taskDateTo'] == null
          ? null
          : DateTime.parse(json['taskDateTo'] as String),
      supplyStatus: (json['supplyStatus'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$ContractStatusEnumMap, e))
          .toList(),
      plannedContractDateFrom: json['plannedContractDateFrom'] == null
          ? null
          : DateTime.parse(json['plannedContractDateFrom'] as String),
      plannedContractDateTo: json['plannedContractDateTo'] == null
          ? null
          : DateTime.parse(json['plannedContractDateTo'] as String),
      lotNumbers: (json['lotNumbers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      lotNames: (json['lotNames'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      contractNumber: json['contractNumber'] as String?,
      supplier: json['supplier'] as String?,
      deliveryDateFrom: json['deliveryDateFrom'] == null
          ? null
          : DateTime.parse(json['deliveryDateFrom'] as String),
      deliveryDateTo: json['deliveryDateTo'] == null
          ? null
          : DateTime.parse(json['deliveryDateTo'] as String),
      costFrom: (json['costFrom'] as num?)?.toDouble(),
      costTo: (json['costTo'] as num?)?.toDouble(),
      projectId: json['projectId'] as String,
      productIds: (json['productIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      lotIds:
          (json['lotIds'] as List<dynamic>?)?.map((e) => e as String).toList(),
      contractIds: (json['contractIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$ProvisionSearchFiltersImplToJson(
        _$ProvisionSearchFiltersImpl instance) =>
    <String, dynamic>{
      'dateFrom': instance.dateFrom?.toIso8601String(),
      'dateTo': instance.dateTo?.toIso8601String(),
      'drawingNumber': instance.drawingNumber,
      'name': instance.name,
      'material': instance.material,
      'feature': instance.feature,
      'filterType': _$ProvisionsFilterEnumMap[instance.filterType],
      'massFrom': instance.massFrom,
      'massTo': instance.massTo,
      'quantityFrom': instance.quantityFrom,
      'quantityTo': instance.quantityTo,
      'requirements': instance.requirements,
      'materialRequirements': instance.materialRequirements,
      'priority': instance.priority,
      'responsiblePerson': instance.responsiblePerson,
      'taskDateFrom': instance.taskDateFrom?.toIso8601String(),
      'taskDateTo': instance.taskDateTo?.toIso8601String(),
      'supplyStatus': instance.supplyStatus
          ?.map((e) => _$ContractStatusEnumMap[e]!)
          .toList(),
      'plannedContractDateFrom':
          instance.plannedContractDateFrom?.toIso8601String(),
      'plannedContractDateTo':
          instance.plannedContractDateTo?.toIso8601String(),
      'lotNumbers': instance.lotNumbers,
      'lotNames': instance.lotNames,
      'contractNumber': instance.contractNumber,
      'supplier': instance.supplier,
      'deliveryDateFrom': instance.deliveryDateFrom?.toIso8601String(),
      'deliveryDateTo': instance.deliveryDateTo?.toIso8601String(),
      'costFrom': instance.costFrom,
      'costTo': instance.costTo,
      'projectId': instance.projectId,
      'productIds': instance.productIds,
      'lotIds': instance.lotIds,
      'contractIds': instance.contractIds,
    };

const _$ProvisionsFilterEnumMap = {
  ProvisionsFilter.cooperation: 'cooperation',
  ProvisionsFilter.materials: 'materials',
  ProvisionsFilter.assemblyMaterials: 'assembly_materials',
  ProvisionsFilter.purchased: 'purchased',
  ProvisionsFilter.assembly: 'assembly',
};

const _$ContractStatusEnumMap = {
  ContractStatus.draft: 'draft',
  ContractStatus.inProgress: 'in_progress',
  ContractStatus.signed: 'signed',
  ContractStatus.completed: 'completed',
  ContractStatus.cancelled: 'cancelled',
};
