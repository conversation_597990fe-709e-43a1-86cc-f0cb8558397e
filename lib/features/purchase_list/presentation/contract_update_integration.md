# Интеграция обновления контрактов

## Обзор

Была добавлена функциональность обновления контрактов в существующий popup создания контрактов. Теперь один и тот же компонент `CreateContractBody` поддерживает как создание новых контрактов, так и редактирование существующих.

## Изменения в коде

### 1. Модель данных

Добавлена новая модель `ProvisionUpdateContractInput` в `lib/features/purchase_list/data/models/index.dart`:

```dart
@freezed
class ProvisionUpdateContractInput with _$ProvisionUpdateContractInput {
  factory ProvisionUpdateContractInput({
    required String contractId, // ID контракта для обновления
    required List<String> productIds,
    required String contractDate,
    // ... остальные поля
  }) = _ProvisionUpdateContractInput;
}
```

### 2. API метод

Обновлен метод `updateContract` в `PurchaseListRepositoryV2`:

```dart
static Future<Response<dynamic>> updateContract(
  ProvisionUpdateContractInput data,
  List<File> files,
) async {
  // Использует правильный URL: /provisions/contract/update
  // Добавляет contractId в FormData
}
```

### 3. UI компонент

Модифицирован `CreateContractBody` для поддержки редактирования:

- Добавлен параметр `existingContract?: ContractModel`
- Предзаполнение полей данными существующего контракта
- Динамические заголовки и кнопки
- Логика создания vs обновления

### 4. Интеграция в таблицу

В `PurchaseListScreen` добавлена кликабельная ячейка контракта:

- Метод `_buildContractCell()` создает кликабельную ячейку
- При клике на номер контракта открывается диалог редактирования
- Визуальное выделение контрактов с иконкой редактирования

## Использование

### Создание нового контракта

```dart
showBaseDialog(
  context,
  maxWidth: 500,
  builder: (context) => CreateContractBody(
    projectId: projectId,
    selectedProductIds: selectedProductIds,
    onContractCreated: () {
      // Обновить данные
    },
  ),
);
```

### Редактирование существующего контракта

```dart
showBaseDialog(
  context,
  maxWidth: 500,
  builder: (context) => CreateContractBody(
    projectId: projectId,
    selectedProductIds: contract.productIds ?? [],
    existingContract: contract, // Передаем существующий контракт
    onContractCreated: () {
      // Обновить данные
    },
  ),
);
```

### Прямой вызов API

```dart
// Создание
final createInput = ProvisionCreateContractInput(/* ... */);
await PurchaseListRepositoryV2.contract(createInput, files);

// Обновление
final updateInput = ProvisionUpdateContractInput(
  contractId: 'existing_contract_id',
  /* ... остальные поля */
);
await PurchaseListRepositoryV2.updateContract(updateInput, files);
```

## Особенности

1. **Предзаполнение полей**: При редактировании все поля автоматически заполняются данными существующего контракта

2. **Файлы опциональны**: При обновлении контракта файлы не обязательны (в отличие от создания)

3. **Кликабельные ячейки**: В таблице номера контрактов отображаются как кликабельные элементы с иконкой редактирования

4. **Единый интерфейс**: Один компонент для создания и редактирования с динамическими заголовками и кнопками

5. **Типобезопасность**: Отдельные модели для создания и обновления обеспечивают типобезопасность

## Вспомогательные методы

```dart
// Создание ProvisionUpdateContractInput из ProvisionCreateContractInput
final updateInput = PurchaseListRepositoryV2.createUpdateInput(
  contractId,
  createInput,
);
```

## Визуальные улучшения

- Контракты в таблице выделены цветом и имеют иконку редактирования
- Динамические заголовки диалогов ("Создать контракт" / "Редактировать контракт")
- Соответствующие сообщения об успехе ("создан" / "обновлен")
