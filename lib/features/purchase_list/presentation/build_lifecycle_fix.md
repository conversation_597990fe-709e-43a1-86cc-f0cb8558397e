# Исправление ошибки setState() during build

## Проблема

При интеграции компонента `ProductDetailsSection` возникала ошибка:

```
setState() or markNeedsBuild() called during build.
This CreateContractBody widget cannot be marked as needing to build because the framework is already in the process of building widgets.
```

## Причина

Ошибка возникала из-за того, что в процессе построения виджета вызывались методы, которые приводили к вызову `setState()` в родительском компоненте:

1. В `initState()` вызывался `_initializeProductDetails()`
2. В `didUpdateWidget()` также вызывался `_initializeProductDetails()`
3. Метод `_initializeProductDetails()` вызывал `_notifyChange()`
4. `_notifyChange()` вызывал `onProductDetailsChanged` callback
5. Callback в родительском компоненте вызывал `setState()`

## Решение

### 1. Отложенная инициализация

Использование `WidgetsBinding.instance.addPostFrameCallback()` для отложения операций до завершения текущего цикла построения:

```dart
@override
void initState() {
  super.initState();
  // Отложенная инициализация после завершения построения
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _initializeProductDetails();
  });
}
```

### 2. Безопасное обновление в didUpdateWidget

```dart
@override
void didUpdateWidget(ProductDetailsSection oldWidget) {
  super.didUpdateWidget(oldWidget);
  if (oldWidget.selectedProductIds != widget.selectedProductIds) {
    // Отложенная инициализация после завершения построения
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProductDetails();
    });
  }
}
```

### 3. Проверка mounted состояния

Добавление проверок `mounted` во все методы, которые могут вызывать `setState`:

```dart
void _notifyChange() {
  if (mounted) {
    widget.onProductDetailsChanged(_productDetails);
  }
}

void _updateProductDetail(int index, ProductDetail updatedDetail) {
  if (mounted && index >= 0 && index < _productDetails.length) {
    setState(() {
      _productDetails[index] = updatedDetail;
    });
    _notifyChange();
  }
}
```

### 4. Отложенное обновление в родительском компоненте

В `CreateContractBody` также используется отложенное обновление:

```dart
ProductDetailsSection(
  selectedProductIds: widget.selectedProductIds,
  productDetails: _productDetails,
  onProductDetailsChanged: (details) {
    // Отложенное обновление состояния
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _productDetails = details;
        });
      }
    });
  },
),
```

### 5. Умная инициализация с отслеживанием изменений

Оптимизация метода `_initializeProductDetails()` для избежания ненужных уведомлений:

```dart
void _initializeProductDetails() {
  _productDetails = List.from(widget.productDetails);
  
  bool hasChanges = false;
  
  // Создаем записи для новых продуктов
  for (final productId in widget.selectedProductIds) {
    if (!_productDetails.any((detail) => detail.productId == productId)) {
      _productDetails.add(ProductDetail(/* ... */));
      hasChanges = true;
    }
  }
  
  // Удаляем записи для неактуальных продуктов
  final initialLength = _productDetails.length;
  _productDetails.removeWhere(/* ... */);
  if (_productDetails.length != initialLength) {
    hasChanges = true;
  }
  
  // Уведомляем только при наличии изменений
  if (hasChanges) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _notifyChange();
    });
  }
}
```

## Результат

После применения этих исправлений:

1. ✅ Ошибка `setState() during build` устранена
2. ✅ Компонент корректно инициализируется
3. ✅ Обновления происходят безопасно
4. ✅ Производительность оптимизирована (избегаем ненужных перестроений)
5. ✅ Код стал более устойчивым к race conditions

## Лучшие практики

1. **Всегда используйте `addPostFrameCallback`** для операций, которые могут вызвать `setState` во время построения
2. **Проверяйте `mounted`** перед вызовом `setState`
3. **Отслеживайте изменения** и уведомляйте только при необходимости
4. **Валидируйте индексы** перед обращением к элементам списка
5. **Используйте отложенные операции** в callback'ах, которые могут вызываться во время построения

## Файлы

- `product_details_section.dart` - основные исправления
- `create_contract.dart` - исправления в родительском компоненте
- `build_lifecycle_fix.md` - данная документация
