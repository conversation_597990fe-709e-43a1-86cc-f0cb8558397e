import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/domain/column.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class FilterPanel extends StatefulWidget {
  final String projectId;
  final int? columnIndex;

  const FilterPanel({
    super.key,
    required this.projectId,
    this.columnIndex,
  });

  @override
  State<FilterPanel> createState() => _FilterPanelState();
}

class _FilterPanelState extends State<FilterPanel> {
  late SearchFiltersModel _filters;
  final _searchController = TextEditingController();
  final _rangeFromController = TextEditingController();
  final _rangeToController = TextEditingController();
  final List<String> _selectedOptions = [];
  DateTime? _dateFrom;
  DateTime? _dateTo;

  // Mapping for feature names
  final Map<String, String> _featureJsonToName = {
    for (var feature in ParametersFeatureType.values)
      feature.getJsonnify(): feature.getName()
  };
  final Map<String, String> _featureNameToJson = {
    for (var feature in ParametersFeatureType.values)
      feature.getName(): feature.getJsonnify()
  };

  @override
  void initState() {
    super.initState();
    _initializeFilters();
    _initializeControllers();
    _fetchOptions();
  }

  void _initializeFilters() {
    final state = context.read<BlocPurchaseList>().state;
    _filters = state.searchFilters ??
        SearchFiltersModel(
          projectId: widget.projectId,
          filterType: ProvisionsFilter.cooperation,
        );
  }

  void _initializeControllers() {
    if (_isRangeFilter()) {
      final range = _getRangeValues();
      _rangeFromController.text = range.from?.toString() ?? '';
      _rangeToController.text = range.to?.toString() ?? '';
    } else if (_isDateFilter()) {
      _dateFrom = _getDateValues().from;
      _dateTo = _getDateValues().to;
    } else {
      _searchController.text = _getSearchValue();
      _selectedOptions.addAll(_getInitialOptions());
    }
  }

  ({double? from, double? to}) _getRangeValues() {
    switch (widget.columnIndex) {
      case 5:
        return (from: _filters.massFrom, to: _filters.massTo);
      case 6:
        return (
          from: _filters.quantityFrom?.toDouble(),
          to: _filters.quantityTo?.toDouble()
        );
      case 7:
        return (from: _filters.totalMassFrom, to: _filters.totalMassTo);
      default:
        return (from: _filters.costFrom, to: _filters.costTo);
    }
  }

  ({DateTime? from, DateTime? to}) _getDateValues() {
    switch (widget.columnIndex) {
      case 0:
        return (from: _filters.dateFrom, to: _filters.dateTo);
      case 13:
        return (from: _filters.taskDateFrom, to: _filters.taskDateTo);
      case 15:
        return (
          from: _filters.plannedContractDateFrom,
          to: _filters.plannedContractDateTo
        );
      case 20:
        return (from: _filters.deliveryDateFrom, to: _filters.deliveryDateTo);
      default:
        return (from: null, to: null);
    }
  }

  String _getSearchValue() {
    if (widget.columnIndex == null) return _filters.query ?? '';
    switch (widget.columnIndex) {
      case 4:
        return _filters.features
                ?.map((e) => _featureJsonToName[e.getJsonnify()] ?? '')
                .join(',') ??
            '';
      case 1:
        return _filters.drawingNumbers?.join(',') ??
            _filters.drawingNumber ??
            '';
      case 2:
        return _filters.names?.join(',') ?? _filters.name ?? '';
      case 3:
        return _filters.materials?.join(',') ?? _filters.material ?? '';
      case 8:
        return _filters.requirements?.join(',') ?? _filters.requirement ?? '';
      case 9:
        return _filters.materialRequirements?.join(',') ??
            _filters.materialRequirement ??
            '';
      case 11:
        return _filters.priorities?.join(',') ??
            _filters.priority?.toString() ??
            '';
      case 12:
        return _filters.responsiblePersons?.join(',') ??
            _filters.responsiblePerson ??
            '';
      case 13:
        return _filters.contractNumbers?.join(',') ??
            _filters.contractNumber ??
            '';
      case 14:
        return _filters.suppliers?.join(',') ?? _filters.supplier ?? '';
      case 20:
        return _filters.lotNumbers?.join(',') ?? '';
      case 21:
        return _filters.lotNames?.join(',') ?? '';
      case 22:
        return _filters.lotIds?.join(',') ?? '';
      case 23:
        return _filters.contractIds?.join(',') ?? '';
      case 24:
        return _filters.parentNames?.join(',') ?? _filters.parentName ?? '';
      default:
        return '';
    }
  }

  List<String> _getInitialOptions() {
    if (widget.columnIndex == null) {
      return _filters.filterType != null
          ? [_filters.filterType!.toString()]
          : [];
    }
    switch (widget.columnIndex) {
      case 4:
        return _filters.features
                ?.map((e) => _featureJsonToName[e.getJsonnify()] ?? '')
                .toList() ??
            [];
      case 1:
        return _filters.drawingNumbers ?? [];
      case 2:
        return _filters.names ?? [];
      case 3:
        return _filters.materials ?? [];
      case 8:
        return _filters.requirements ?? [];
      case 9:
        return _filters.materialRequirements ?? [];
      case 11:
        return _filters.priorities?.map((e) => e.toString()).toList() ?? [];
      case 12:
        return _filters.responsiblePersons ?? [];
      case 13:
        return _filters.contractNumbers ?? [];
      case 14:
        return _filters.suppliers ?? [];
      case 20:
        return _filters.lotNumbers?.map((e) => e.toString()).toList() ?? [];
      case 21:
        return _filters.lotNames ?? [];
      case 22:
        return _filters.lotIds ?? [];
      case 23:
        return _filters.contractIds ?? [];
      case 24:
        return _filters.parentNames ?? [];
      default:
        return [];
    }
  }

  bool _isRangeFilter() {
    return widget.columnIndex == 5 ||
        widget.columnIndex == 6 ||
        widget.columnIndex == 7 ||
        (widget.columnIndex == null &&
            (_filters.costFrom != null || _filters.costTo != null));
  }

  bool _isDateFilter() {
    return widget.columnIndex == 0 ||
        widget.columnIndex == 14 ||
        widget.columnIndex == 16 ||
        widget.columnIndex == 21 ||
        widget.columnIndex == 27;
  }

  void _fetchOptions() {
    final column = widget.columnIndex != null
        ? _mapColumnIndexToProvisionColumn(widget.columnIndex!)
        : null;
    context.read<BlocPurchaseList>().add(
          FetchColumnOptions(
            ProvisionsColumnOptionsInput(
              projectId: widget.projectId,
              column: column,
              search: _searchController.text.isEmpty
                  ? null
                  : _searchController.text,
              filterType: _filters.filterType,
            ),
          ),
        );
  }

  ProvisionColumn? _mapColumnIndexToProvisionColumn(int columnIndex) {
    const map = {
      1: ProvisionColumn.drawingNumber,
      2: ProvisionColumn.name,
      3: ProvisionColumn.material,
      4: ProvisionColumn.feature,
      8: ProvisionColumn.requirements,
      9: ProvisionColumn.materialRequirements,
      11: ProvisionColumn.priority,
      13: ProvisionColumn.contractNumber,
      14: ProvisionColumn.supplier,
      16: ProvisionColumn.lotNumber,
      17: ProvisionColumn.lotName,
      22: ProvisionColumn.contractNumber,
      23: ProvisionColumn.parentName,
    };
    return map[columnIndex];
  }

  @override
  void dispose() {
    _searchController.dispose();
    _rangeFromController.dispose();
    _rangeToController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    var filters = _filters.copyWith(
      filterType: widget.columnIndex == null && _selectedOptions.isNotEmpty
          ? ProvisionsFilter.values.firstWhere(
              (e) => _selectedOptions.contains(e.toString()),
              orElse: () => _filters.filterType!,
            )
          : _filters.filterType,
      query: widget.columnIndex == null
          ? _searchController.text.isEmpty
              ? null
              : _searchController.text
          : _filters.query,
    );

    if (_isRangeFilter()) {
      final from = _rangeFromController.text.isEmpty
          ? null
          : double.tryParse(_rangeFromController.text);
      final to = _rangeToController.text.isEmpty
          ? null
          : double.tryParse(_rangeToController.text);
      switch (widget.columnIndex) {
        case 5:
          filters = filters.copyWith(massFrom: from, massTo: to);
          break;
        case 6:
          filters = filters.copyWith(
              quantityFrom: from?.toInt(), quantityTo: to?.toInt());
          break;
        default:
          filters = filters.copyWith(costFrom: from, costTo: to);
      }
    } else if (_isDateFilter()) {
      switch (widget.columnIndex) {
        case 0:
          filters = filters.copyWith(dateFrom: _dateFrom, dateTo: _dateTo);
          break;
        case 13:
          filters =
              filters.copyWith(taskDateFrom: _dateFrom, taskDateTo: _dateTo);
          break;
        case 15:
          filters = filters.copyWith(
              plannedContractDateFrom: _dateFrom,
              plannedContractDateTo: _dateTo);
          break;
        case 20:
          filters = filters.copyWith(
              deliveryDateFrom: _dateFrom, deliveryDateTo: _dateTo);
          break;
      }
    } else if (widget.columnIndex != null) {
      final values = _selectedOptions.isNotEmpty ? _selectedOptions : null;
      final search =
          _searchController.text.isEmpty ? null : _searchController.text;
      switch (widget.columnIndex) {
        case 4:
          final features = values
              ?.map((name) => ParametersFeatureType.values.firstWhere(
                  (f) => f.getJsonnify() == _featureNameToJson[name]))
              .toList();
          filters = filters.copyWith(features: features);
          break;
        case 1:
          filters = filters.copyWith(
              drawingNumbers: values,
              drawingNumber: values == null ? search : null);
          break;
        case 2:
          filters = filters.copyWith(
              names: values, name: values == null ? search : null);
          break;
        case 3:
          filters = filters.copyWith(
              materials: values, material: values == null ? search : null);
          break;
        case 8:
          filters = filters.copyWith(
              requirements: values,
              requirement: values == null ? search : null);
          break;
        case 9:
          filters = filters.copyWith(
              materialRequirements: values,
              materialRequirement: values == null ? search : null);
          break;
        case 11:
          filters = filters.copyWith(
              priorities: values?.map((e) => int.tryParse(e) ?? 0).toList(),
              priority: values == null ? int.tryParse(search ?? '') : null);
          break;
        case 12:
          filters = filters.copyWith(
              responsiblePersons: values,
              responsiblePerson: values == null ? search : null);
          break;
        case 13:
          filters = filters.copyWith(
              contractNumbers: values,
              contractNumber: values == null ? search : null);
          break;
        case 14:
          filters = filters.copyWith(
              suppliers: values, supplier: values == null ? search : null);
          break;
        case 19:
          filters = filters.copyWith(
              lotNumbers: values?.map((e) => int.tryParse(e) ?? 0).toList());
          break;
        case 20:
          filters = filters.copyWith(lotNames: values);
          break;
        case 21:
          filters = filters.copyWith(lotIds: values);
          break;
        case 22:
          filters = filters.copyWith(contractIds: values);
          break;
        case 23:
          filters = filters.copyWith(
              parentNames: values, parentName: values == null ? search : null);
          break;
      }
    }

    context.read<BlocPurchaseList>().add(ApplyFilters(filters));
    Navigator.of(context).pop();
  }

  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _rangeFromController.clear();
      _rangeToController.clear();
      _selectedOptions.clear();
      _dateFrom = null;
      _dateTo = null;
      _filters = SearchFiltersModel(
          projectId: widget.projectId,
          filterType: ProvisionsFilter.cooperation);
      context.read<BlocPurchaseList>().add(ClearFilters());
      _fetchOptions();
    });
  }

  Future<void> _selectDate(BuildContext context, bool isFrom) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() => isFrom ? _dateFrom = picked : _dateTo = picked);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<BlocPurchaseList, BlocPurchaseListState>(
      listener: (context, state) {
        if (state.options.items != null &&
            !_isRangeFilter() &&
            !_isDateFilter()) {
          setState(() {
            if (state.options.items!.isNotEmpty &&
                !_selectedOptions
                    .every((opt) => state.options.items!.contains(opt))) {
              _selectedOptions.clear();
            }
          });
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.columnIndex == null
                    ? 'Фильтры списка закупок'
                    : PurchaseListTableData.columns[widget.columnIndex!].name ??
                        'Фильтр',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_isRangeFilter())
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _rangeFromController,
                    decoration: const InputDecoration(
                        labelText: 'От', border: OutlineInputBorder()),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _rangeToController,
                    decoration: const InputDecoration(
                        labelText: 'До', border: OutlineInputBorder()),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            )
          else if (_isDateFilter())
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context, true),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                          labelText: 'От', border: OutlineInputBorder()),
                      child: Text(_dateFrom != null
                          ? DateFormat('dd.MM.yyyy').format(_dateFrom!)
                          : 'Выберите дату'),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context, false),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                          labelText: 'До', border: OutlineInputBorder()),
                      child: Text(_dateTo != null
                          ? DateFormat('dd.MM.yyyy').format(_dateTo!)
                          : 'Выберите дату'),
                    ),
                  ),
                ),
              ],
            )
          else ...[
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Поиск',
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _fetchOptions();
                        },
                      )
                    : null,
              ),
              onChanged: (value) => _fetchOptions(),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: BlocBuilder<BlocPurchaseList, BlocPurchaseListState>(
                builder: (context, state) {
                  final options = widget.columnIndex == 4
                      ? (state.options.items ?? [])
                          .map((json) => _featureJsonToName[json] ?? json)
                          .toList()
                      : state.options.items ?? [];
                  if (state.isLoading) {
                    return const Center(
                        child: CircularProgressIndicator.adaptive());
                  }
                  if (options.isEmpty) {
                    return const Center(child: Text('Нет доступных опций'));
                  }
                  return ListView.builder(
                    shrinkWrap: true,
                    itemCount: options.length,
                    itemBuilder: (context, index) {
                      final option = options[index];
                      return CheckboxListTile(
                        value: _selectedOptions.contains(option),
                        title: Text(option),
                        onChanged: (value) {
                          setState(() {
                            if (value == true) {
                              _selectedOptions.add(option);
                            } else {
                              _selectedOptions.remove(option);
                            }
                            _searchController.text = _selectedOptions.isNotEmpty
                                ? _selectedOptions.join(',')
                                : '';
                          });
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GhostButton(
                onTap: _clearFilters,
                child: const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text('Сбросить'),
                ),
              ),
              Row(
                children: [
                  GhostButton(
                    onTap: () => Navigator.of(context).pop(),
                    child: const Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Text('Отмена'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  CustomElevatedButton(
                    text: 'Применить',
                    onPressed: _applyFilters,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

void showFilterPanel(BuildContext context, String projectId) {
  showBaseDialog(
    context,
    builder: (context) => FilterPanel(projectId: projectId),
    maxWidth: 800,
  );
}
