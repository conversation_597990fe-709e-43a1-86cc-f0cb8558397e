import 'dart:io';

import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/client/data/repositories/index.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/features/purchase_list/presentation/product_details_section_v2.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class CreateContractBody extends StatefulWidget {
  const CreateContractBody({
    super.key,
    required this.projectId,
    required this.selectedProductIds,
    required this.onContractCreated,
    this.existingContract,
  });

  final String projectId;
  final List<String> selectedProductIds;
  final VoidCallback onContractCreated;
  final ContractModel? existingContract;

  @override
  State<CreateContractBody> createState() => _CreateContractBodyState();
}

class _CreateContractBodyState extends State<CreateContractBody> {
  final _formKey = GlobalKey<FormState>();
  // final _contractNumberController = TextEditingController();
  final _contractDateController = TextEditingController();
  final _contractPriceController = TextEditingController();
  final _contractStartDateController = TextEditingController();
  final _contractEndDateController = TextEditingController();
  final _supplierSearchController = TextEditingController();
  final _plannedDeliveryDateController = TextEditingController();
  final _notesController = TextEditingController();
  List<File> _selectedFiles = [];
  bool _isLoading = false;

  // Поставщики
  final List<ClientModel> _suppliers = [];
  ClientModel? _selectedSupplier;
  final _debouncer = Debouncer();

  // Детали продуктов
  List<ProductDetail> _productDetails = [];

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.existingContract != null) {
      final contract = widget.existingContract!;

      // Предзаполняем поля данными существующего контракта
      if (contract.contractDate != null) {
        _contractDateController.text =
            '${contract.contractDate!.year}-${contract.contractDate!.month.toString().padLeft(2, '0')}-${contract.contractDate!.day.toString().padLeft(2, '0')}';
      }

      if (contract.contractPrice != null) {
        _contractPriceController.text = contract.contractPrice.toString();
      }

      // Для дат начала и окончания нужно проверить, есть ли они в модели
      // Пока что оставим пустыми, так как в ContractModel нет contractStartDate и contractEndDate

      if (contract.plannedDeliveryDate != null) {
        _plannedDeliveryDateController.text =
            '${contract.plannedDeliveryDate!.year}-${contract.plannedDeliveryDate!.month.toString().padLeft(2, '0')}-${contract.plannedDeliveryDate!.day.toString().padLeft(2, '0')}';
      }

      if (contract.notes != null) {
        _notesController.text = contract.notes!;
      }

      // Устанавливаем поставщика
      if (contract.supplier != null) {
        _selectedSupplier = contract.supplier;
        _supplierSearchController.text = contract.supplier!.name ?? '';
      }

      // Инициализируем детали продуктов из существующего контракта
      if (contract.productDetails != null &&
          contract.productDetails!.isNotEmpty) {
        _productDetails.clear();
        _productDetails.addAll(contract.productDetails!);
      }
    } else {
      // Для создания нового контракта устанавливаем текущую дату
      final now = DateTime.now();
      _contractDateController.text =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    // _contractNumberController.dispose();
    _contractDateController.dispose();
    _contractPriceController.dispose();
    _contractStartDateController.dispose();
    _contractEndDateController.dispose();
    _supplierSearchController.dispose();
    _plannedDeliveryDateController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _searchSuppliers() async {
    if (_supplierSearchController.text.isEmpty) {
      setState(() {
        _suppliers.clear();
      });
      return;
    }

    final result = await ClientRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _supplierSearchController.text,
        clientType: ClientType.supplier,
      ),
    ));

    setState(() {
      _suppliers.clear();
      if (result?.data?.items != null) {
        _suppliers.addAll(result!.data!.items!);
      }
    });
  }

  void _selectSupplier(ClientModel supplier) {
    setState(() {
      _selectedSupplier = supplier;
      _supplierSearchController.text = supplier.name ?? '';
      _suppliers.clear();
    });
  }

  Future<void> _pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      // type: FileType.custom,
      // allowedExtensions: ['pdf'],
      allowMultiple: true,
    );

    if (result != null && result.files.isNotEmpty) {
      setState(() {
        _selectedFiles = result.files
            .where((file) => file.path != null)
            .map((file) => File(file.path!))
            .toList();
      });
    }
  }

  Future<void> _pickDate(
      BuildContext context, TextEditingController controller) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (pickedDate != null) {
      final formattedDate =
          '${pickedDate.year}-${pickedDate.month.toString().padLeft(2, '0')}-${pickedDate.day.toString().padLeft(2, '0')}';
      controller.text = formattedDate;
    }
  }

  Future<void> _createContract() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final isUpdate = widget.existingContract != null;

    // Для создания нового контракта файлы обязательны, для обновления - нет
    if (!isUpdate && _selectedFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Пожалуйста, выберите хотя бы один PDF-файл')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      late final Response response;

      if (isUpdate) {
        // Обновление существующего контракта
        final updateInput = ProvisionUpdateContractInput(
          contractId: widget.existingContract!.id!,
          productIds: widget.selectedProductIds,
          contractDate: _contractDateController.text.trim(),
          contractPrice: double.parse(_contractPriceController.text.trim()),
          contractStartDate: _contractStartDateController.text.trim().isNotEmpty
              ? _contractStartDateController.text.trim()
              : null,
          contractEndDate: _contractEndDateController.text.trim().isNotEmpty
              ? _contractEndDateController.text.trim()
              : null,
          supplierId: _selectedSupplier?.id,
          plannedDeliveryDate:
              _plannedDeliveryDateController.text.trim().isNotEmpty
                  ? _plannedDeliveryDateController.text.trim()
                  : null,
          notes: _notesController.text.trim().isNotEmpty
              ? _notesController.text.trim()
              : null,
          productDetails: _productDetails,
          paymentDetails: [],
        );

        response = await PurchaseListRepositoryV2.updateContract(
            updateInput, _selectedFiles);
      } else {
        // Создание нового контракта
        final contractInput = ProvisionCreateContractInput(
          productIds: widget.selectedProductIds,
          contractDate: _contractDateController.text.trim(),
          contractPrice: double.parse(_contractPriceController.text.trim()),
          contractStartDate: _contractStartDateController.text.trim().isNotEmpty
              ? _contractStartDateController.text.trim()
              : null,
          contractEndDate: _contractEndDateController.text.trim().isNotEmpty
              ? _contractEndDateController.text.trim()
              : null,
          supplierId: _selectedSupplier?.id,
          plannedDeliveryDate:
              _plannedDeliveryDateController.text.trim().isNotEmpty
                  ? _plannedDeliveryDateController.text.trim()
                  : null,
          notes: _notesController.text.trim().isNotEmpty
              ? _notesController.text.trim()
              : null,
          productDetails: _productDetails,
          paymentDetails: [],
        );

        response = await PurchaseListRepositoryV2.contract(
            contractInput, _selectedFiles);
      }

      if (mounted) {
        if (response.statusCode == 200 || response.statusCode == 201) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(isUpdate
                    ? 'Контракт успешно обновлен!'
                    : 'Контракт успешно создан!')),
          );
          widget.onContractCreated();
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Ошибка сервера: ${response.statusMessage ?? 'Неизвестная ошибка'}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = isUpdate
            ? 'Ошибка при обновлении контракта'
            : 'Ошибка при создании контракта';
        if (e.toString().contains('DioException')) {
          errorMessage = 'Ошибка сети или сервера';
        } else if (e.toString().contains('FormatException')) {
          errorMessage = 'Ошибка в формате данных';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$errorMessage: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.existingContract != null
                  ? 'Редактировать контракт'
                  : 'Создать контракт',
              style: Fonts.titleSmall,
            ),
            // const SizedBox(height: 12.0),
            // TextFormField(
            //   controller: _contractNumberController,
            //   decoration: const InputDecoration(
            //     labelText: 'Номер договора/спецификации/счета',
            //     border: OutlineInputBorder(),
            //   ),
            //   style: Fonts.bodyMedium,
            //   validator: (value) {
            //     if (value == null || value.trim().isEmpty) {
            //       return 'Введите номер договора';
            //     }
            //     return null;
            //   },
            // ),
            const SizedBox(height: 12.0),
            TextFormField(
              controller: _contractDateController,
              decoration: const InputDecoration(
                labelText: 'Дата заключения (ГГГГ-ММ-ДД)',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              style: Fonts.bodyMedium,
              readOnly: true,
              onTap: () => _pickDate(context, _contractDateController),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Выберите дату заключения';
                }
                if (!RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(value)) {
                  return 'Формат даты: ГГГГ-ММ-ДД';
                }
                return null;
              },
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              controller: _contractPriceController,
              decoration: const InputDecoration(
                labelText: 'Общая сумма контракта',
                border: OutlineInputBorder(),
              ),
              style: Fonts.bodyMedium,
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Введите сумму контракта';
                }
                if (double.tryParse(value) == null ||
                    double.parse(value) <= 0) {
                  return 'Введите корректную сумму';
                }
                return null;
              },
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              controller: _contractStartDateController,
              decoration: const InputDecoration(
                labelText: 'Дата начала (ГГГГ-ММ-ДД, опционально)',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              style: Fonts.bodyMedium,
              readOnly: true,
              onTap: () => _pickDate(context, _contractStartDateController),
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  if (!RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(value)) {
                    return 'Формат даты: ГГГГ-ММ-ДД';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              controller: _contractEndDateController,
              decoration: const InputDecoration(
                labelText: 'Дата окончания (ГГГГ-ММ-ДД, опционально)',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              style: Fonts.bodyMedium,
              readOnly: true,
              onTap: () => _pickDate(context, _contractEndDateController),
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  if (!RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(value)) {
                    return 'Формат даты: ГГГГ-ММ-ДД';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 8.0),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  controller: _supplierSearchController,
                  decoration: InputDecoration(
                    labelText: 'Поставщик (опционально)',
                    border: const OutlineInputBorder(),
                    suffixIcon: _selectedSupplier != null
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _selectedSupplier = null;
                                _supplierSearchController.clear();
                                _suppliers.clear();
                              });
                            },
                          )
                        : const Icon(Icons.search),
                  ),
                  style: Fonts.bodyMedium,
                  onChanged: (value) {
                    if (_selectedSupplier != null) {
                      setState(() {
                        _selectedSupplier = null;
                      });
                    }
                    _debouncer.run(_searchSuppliers);
                  },
                ),
                if (_suppliers.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.only(top: 4.0),
                    constraints: const BoxConstraints(
                      maxHeight: 200.0, // Ограничиваем высоту
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: _suppliers.length,
                      itemBuilder: (context, index) {
                        final supplier = _suppliers[index];
                        return ListTile(
                          title: Text(
                            supplier.name ?? 'Без названия',
                            style: Fonts.bodyMedium,
                          ),
                          onTap: () => _selectSupplier(supplier),
                        );
                      },
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              controller: _plannedDeliveryDateController,
              decoration: const InputDecoration(
                labelText: 'Плановая дата поставки (ГГГГ-ММ-ДД, опционально)',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              style: Fonts.bodyMedium,
              readOnly: true,
              onTap: () => _pickDate(context, _plannedDeliveryDateController),
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  if (!RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(value)) {
                    return 'Формат даты: ГГГГ-ММ-ДД';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Примечания (опционально)',
                border: OutlineInputBorder(),
              ),
              style: Fonts.bodyMedium,
              maxLines: 3,
            ),
            const SizedBox(height: 16.0),

            // Секция с деталями продуктов
            ProductDetailsSectionV2(
              selectedProductIds: widget.selectedProductIds,
              productDetails: _productDetails,
              onProductDetailsChanged: (details) {
                // Отложенное обновление состояния
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    setState(() {
                      _productDetails = details;
                    });
                  }
                });
              },
            ),

            const SizedBox(height: 16.0),
            Row(
              children: [
                Expanded(
                  child: Text(
                    _selectedFiles.isNotEmpty
                        ? 'Файлы (${_selectedFiles.length}): ${_selectedFiles.map((file) => file.path.split('/').last).join(', ')}'
                        : 'Файлы не выбраны',
                    style: Fonts.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8.0),
                SizedBox(
                  width: 120,
                  child: ElevatedButton(
                    onPressed: _pickFile,
                    child: const Text('Выбрать файлы'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12.0),
            Row(
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Отмена',
                    style: Fonts.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: CustomElevatedButton(
                    onPressed: () {
                      if (!_isLoading) _createContract();
                    },
                    text: _isLoading
                        ? (widget.existingContract != null
                            ? 'Обновление...'
                            : 'Создание...')
                        : (widget.existingContract != null
                            ? 'Обновить'
                            : 'Создать'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
