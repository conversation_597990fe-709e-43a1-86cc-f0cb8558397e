# Управление статусами контрактов

## Обзор

Добавлена полная функциональность управления статусами контрактов, включая изменение статуса отдельных контрактов и массовое изменение статусов для нескольких контрактов одновременно.

## Компоненты

### 1. ChangeContractStatusDialog

Диалог для изменения статуса одного контракта.

**Особенности:**
- Отображение информации о контракте (номер, поставщик, сумма, текущий статус)
- Выбор действия из доступных `ContractAction`
- Опциональное поле для указания причины изменения
- Валидация и обработка ошибок

**Использование:**
```dart
showBaseDialog(
  context,
  maxWidth: 450,
  builder: (context) => ChangeContractStatusDialog(
    contract: contract,
    onStatusChanged: () {
      // Обновить данные
    },
  ),
);
```

### 2. BatchChangeContractStatusDialog

Диалог для массового изменения статуса нескольких контрактов.

**Особенности:**
- Отображение списка контрактов (до 5 с возможностью свернуть)
- Единое действие для всех контрактов
- Обработка ошибок для каждого контракта отдельно
- Отчет о результатах (успешные/неудачные изменения)

**Использование:**
```dart
showBaseDialog(
  context,
  maxWidth: 500,
  builder: (context) => BatchChangeContractStatusDialog(
    contracts: contracts,
    onStatusChanged: () {
      // Обновить данные
    },
  ),
);
```

## Интеграция в UI

### 1. Контекстное меню для ячеек контрактов

В таблице продуктов ячейки с номерами контрактов теперь поддерживают:
- **Левый клик**: Редактирование контракта
- **Правый клик**: Контекстное меню с опциями:
  - "Редактировать контракт"
  - "Изменить статус"

### 2. Главное меню действий

В основном меню действий добавлен пункт:
- **"Изменить статус контрактов (N)"** - появляется только когда выбраны продукты с контрактами

## Доступные действия (ContractAction)

```dart
enum ContractAction {
  confirm,  // Подтвердить
  revert,   // Отменить  
  cancel;   // Отменить
}
```

## API методы

### Изменение статуса контракта

```dart
// Одиночное изменение
final statusChangeData = ProvisionChangeContractStatusInput(
  contractId: 'contract_id',
  action: ContractAction.confirm,
  reason: 'Причина изменения',
);

final response = await PurchaseListRepositoryV2.changeContractStatus(
  statusChangeData,
);
```

### Массовое изменение

```dart
// Для каждого контракта отдельно
for (final contract in contracts) {
  final statusChangeData = ProvisionChangeContractStatusInput(
    contractId: contract.id,
    action: selectedAction,
    reason: reason,
  );
  
  await PurchaseListRepositoryV2.changeContractStatus(statusChangeData);
}
```

## Логика работы

### 1. Получение контрактов из выбранных продуктов

```dart
List<ContractModel> _getSelectedProductsWithContracts(BlocPurchaseListState state) {
  final selectedProductIds = state.selectedProducts.keys.toSet();
  final contracts = <ContractModel>[];
  
  for (final product in state.products) {
    if (selectedProductIds.contains(product.uniqueId) && product.contract != null) {
      // Избегаем дубликатов контрактов
      if (!contracts.any((c) => c.id == product.contract!.id)) {
        contracts.add(product.contract!);
      }
    }
  }
  
  return contracts;
}
```

### 2. Выбор типа диалога

```dart
void _showContractStatusBatchDialog(List<ContractModel> contracts) {
  if (contracts.isEmpty) return;
  
  if (contracts.length == 1) {
    // Одиночный диалог для одного контракта
    _showChangeContractStatusDialog(contracts.first);
  } else {
    // Массовый диалог для нескольких контрактов
    _showBatchContractStatusDialog(contracts);
  }
}
```

## Обработка ошибок

### Одиночное изменение
- Показ конкретной ошибки сервера
- Различение типов ошибок (сеть, сервер, валидация)

### Массовое изменение
- Продолжение обработки при ошибке отдельного контракта
- Сбор списка неудачных операций
- Итоговый отчет с количеством успешных и неудачных изменений

## Пример полного workflow

1. **Выбор продуктов** с контрактами в таблице
2. **Открытие меню** действий
3. **Клик** на "Изменить статус контрактов (N)"
4. **Выбор действия** в диалоге
5. **Указание причины** (опционально)
6. **Подтверждение** изменения
7. **Получение результата** и обновление данных

## Файлы

- `change_contract_status_dialog.dart` - Диалог для одного контракта
- `batch_change_contract_status_dialog.dart` - Диалог для нескольких контрактов
- `index.dart` - Интеграция в основной экран
- `new.dart` - API метод `changeContractStatus`
