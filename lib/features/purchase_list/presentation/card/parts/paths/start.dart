// import 'dart:ui' as ui;

import 'package:flutter/material.dart';

//Add this CustomPaint widget to the Widget Tree
// CustomPaint(
//     size = Size(WIDTH, (WIDTH*3).toDouble()), //You can Replace [WIDTH] with your desired width for Custom Paint and height will be calculated automatically
//     painter = RPSCustomPainter(),
// )

//Copy this CustomPainter code to the Bottom of the File
class BranchPathStart extends CustomPainter {
  BranchPathStart({
    this.color,
    this.strokeWidth = 2.0,
  });

  final Color? color;
  final double strokeWidth;

  @override
  void paint(Canvas canvas, Size size) {
    Path path_0 = Path();
    path_0.moveTo(size.width * 0.5000000, size.height * 0.15);
    path_0.lineTo(size.width * 0.5000000, size.height);

    Paint paint0Stroke = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;
    paint0Stroke.color = color ?? Color(0xffcccccc);
    paint0Stroke.strokeCap = StrokeCap.round;
    canvas.drawPath(path_0, paint0Stroke);

    Path path_1 = Path();
    path_1.moveTo(size.width * 0.5000000, size.height * 0.15);
    path_1.lineTo(size.width * 0.5000000, size.height * 0.3472222);
    path_1.cubicTo(
        size.width * 0.5000000,
        size.height * 0.4315986,
        size.width * 0.7052042,
        size.height * 0.5000000,
        size.width * 0.9583333,
        size.height * 0.5000000);
    path_1.lineTo(size.width * 0.9583333, size.height * 0.5000000);

    Paint paint1Stroke = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;
    paint1Stroke.color = color ?? Color(0xffcccccc);
    paint1Stroke.strokeCap = StrokeCap.round;
    canvas.drawPath(path_1, paint1Stroke);

    Path path_2 = Path();
    path_2.moveTo(size.width * 0, size.height * 0.15);
    path_2.lineTo(size.width, size.height * 0.15);

    Paint path2Stroke = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;
    path2Stroke.color = color ?? Color(0xffcccccc);
    path2Stroke.strokeCap = StrokeCap.round;
    canvas.drawPath(path_2, path2Stroke);

    // Paint paint1Fill = Paint()..style = PaintingStyle.fill;
    // paint1Fill.color = Color(0xff000000).withOpacity(1.0);
    // canvas.drawPath(path_1, paint1Fill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
