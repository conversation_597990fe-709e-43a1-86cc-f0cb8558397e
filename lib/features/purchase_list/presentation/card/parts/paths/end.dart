import 'package:flutter/material.dart';

//Copy this CustomPainter code to the Bottom of the File
class BranchPathEnd extends CustomPainter {
  BranchPathEnd({
    this.color,
    this.strokeWidth = 2.0,
  });

  final Color? color;
  final double strokeWidth;

  @override
  void paint(Canvas canvas, Size size) {
    Path path_0 = Path();
    path_0.moveTo(size.width * 0.5000000, 0);
    path_0.lineTo(size.width * 0.5000000, size.height * 0.3472222);
    path_0.cubicTo(
        size.width * 0.5000000,
        size.height * 0.4315986,
        size.width * 0.7052042,
        size.height * 0.5000000,
        size.width * 0.9583333,
        size.height * 0.5000000);
    path_0.lineTo(size.width * 0.9583333, size.height * 0.5000000);

    Paint paint0Stroke = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;
    paint0Stroke.color = color ?? Color(0xffcccccc);
    paint0Stroke.strokeCap = StrokeCap.round;
    canvas.drawPath(path_0, paint0Stroke);

    // Paint paint0Fill = Paint()..style = PaintingStyle.fill;
    // paint0Fill.color = Color(0xff000000).withOpacity(1.0);
    // canvas.drawPath(path_0, paint0Fill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
