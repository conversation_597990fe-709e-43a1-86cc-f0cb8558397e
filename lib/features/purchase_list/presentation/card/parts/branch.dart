import 'package:flutter/material.dart';
import 'package:sphere/features/purchase_list/presentation/card/index.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/paths/end.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/paths/middle.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/paths/start.dart';
import 'package:sphere/shared/styles/colors.dart';

class PurchaseCardBranch extends StatelessWidget {
  const PurchaseCardBranch({
    super.key,
    required this.childType,
    required this.isChild,
    required this.selecting,
    required this.selected,
    this.onSelected,
    this.width = 24.0,
    this.height,
  });

  final PurchaseItemChildType childType;
  final bool isChild;
  final bool selecting;
  final bool selected;
  final void Function(bool selected)? onSelected;
  final double width;
  final double? height;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    if (false) {
      // Проверяем, чтобы height не был Infinity
      final containerHeight = height?.isFinite == true ? height : 24.0;

      CustomPainter getBranchPath() {
        switch (childType) {
          case PurchaseItemChildType.start:
            return BranchPathStart(
              color: isDarkTheme
                  ? AppColors.darkAccentStroke
                  : AppColors.lightAccentStroke,
            );
          case PurchaseItemChildType.middle:
            return BranchPathMiddle(
              color: isDarkTheme
                  ? AppColors.darkAccentStroke
                  : AppColors.lightAccentStroke,
            );
          case PurchaseItemChildType.end:
            return BranchPathEnd(
              color: isDarkTheme
                  ? AppColors.darkAccentStroke
                  : AppColors.lightAccentStroke,
            );
        }
      }

      return Stack(alignment: Alignment.center, children: [
        Container(
          width: width,
          height: containerHeight, // Используем проверенное значение
          color: Colors.amber,
          child: CustomPaint(painter: getBranchPath()),
        ),
        if (selecting)
          SizedBox(
            width: width,
            height: containerHeight, // Используем проверенное значение
            child: Checkbox(
              visualDensity: VisualDensity.compact,
              value: selected,
              onChanged: (selected) {
                onSelected?.call(selected ?? false);
              },
            ),
          )
      ]);
    } else {
      return SizedBox(
        width: isChild ? width + 24.0 + 12.0 : width,
        child: Stack(alignment: Alignment.center, children: [
          Divider(
            thickness: 2.0,
            color: isDarkTheme
                ? AppColors.darkAccentStroke
                : AppColors.lightAccentStroke,
          ),
          if (selecting)
            Checkbox(
              visualDensity: VisualDensity.compact,
              value: selected,
              onChanged: (selected) {
                onSelected?.call(selected ?? false);
              },
            )
        ]),
      );
    }
  }
}
