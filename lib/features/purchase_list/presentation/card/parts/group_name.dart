import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/files/presentation/index.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class PurchaseGroupCardName extends StatelessWidget {
  const PurchaseGroupCardName({
    super.key,
    this.data,
    this.name,
    this.clients,
    this.onSecondaryTapDown,
  });

  final ProvisionLotModel? data;
  final String? name;
  final List<String>? clients;
  final void Function(TapDownDetails)? onSecondaryTapDown;

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Expanded(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Wrap(
                  children: [
                    GhostButton(
                      color: isDarkTheme
                          ? AppColors.darkPrimary
                          : AppColors.lightPrimary,
                      onSecondaryTapDown: onSecondaryTapDown,
                      child: Text(
                        name ?? 'Пустое название группы',
                        style: Fonts.labelMedium.merge(TextStyle(
                            // color: isDarkTheme
                            //     ? AppColors.darkDescription
                            //     : AppColors.lightDescription,
                            )),
                      ),
                    ),
                    if (data?.provisionStatus != null) SizedBox(width: 12.0),
                    if (data?.provisionStatus != null)
                      Text(
                        data!.provisionStatus!.getName(),
                        style: Fonts.labelSmall.merge(TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkSecondary
                              : AppColors.lightSecondary,
                        )),
                      ),
                  ],
                ),
                if (clients != null)
                  Wrap(
                    spacing: 6.0,
                    runSpacing: 6.0,
                    children: clients!.map((client) {
                      return GhostButton(
                        onTap: () {},
                        child: Text(
                          client,
                          style: Fonts.labelSmall.merge(TextStyle(
                            fontSize: 14.0,
                            color: isDarkTheme
                                ? AppColors.darkSecondary
                                : AppColors.lightSecondary,
                          )),
                        ),
                      );
                    }).toList(),
                  ),
                Wrap(
                  spacing: 6.0,
                  runSpacing: 6.0,
                  children: [
                    if (data?.contractDate != null)
                      Text(
                        'Дата контрактации: ${getDateString(data!.contractDate!)}',
                        style: Fonts.bodySmall.merge(TextStyle(
                          fontSize: 14.0,
                        )),
                      ),
                    if (data?.contractDate != null)
                      Text(
                        'Дата поставки лота: ${getDateString(data!.contractEndDate!)}',
                        style: Fonts.bodySmall.merge(TextStyle(
                          fontSize: 14.0,
                        )),
                      ),
                    // if (data?.contractDate != null)
                    //   Text(
                    //     'Дата окончания действия контракта: ${getDateString(data!.contractEndDate!)}',
                    //     style: Fonts.bodySmall.merge(TextStyle(
                    //       fontSize: 14.0,
                    //     )),
                    //   ),
                  ],
                ),
                if (data?.paymentDetails != null &&
                    data!.paymentDetails!.isNotEmpty)
                  SizedBox(height: 8.0),
                if (data?.paymentDetails != null &&
                    data!.paymentDetails!.isNotEmpty)
                  Wrap(runSpacing: 4.0, spacing: 4.0, children: [
                    Text(
                      'Даты оплат:',
                      style: Fonts.labelSmall,
                    ),
                    SizedBox(width: 8.0),
                    ...data!.paymentDetails!.map((payment) {
                          return CustomChip(
                            child: Row(children: [
                              Text(
                                getDateString(
                                    payment.paymentDate ?? DateTime.now()),
                                style: Fonts.labelSmall,
                              ),
                              SizedBox(width: 8.0),
                              Text(
                                '${payment.amount ?? 0.0} ₽',
                                style: Fonts.labelSmall.merge(
                                  TextStyle(
                                    color: isDarkTheme
                                        ? AppColors.darkSecondary
                                        : AppColors.lightSecondary,
                                  ),
                                ),
                              ),
                            ]),
                          );
                        }) ??
                        [],
                  ]),
              ],
            ),
          ),
          Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
            Text(
              'Сумма договора: ${data?.contractPrice ?? data?.lotPrice} ₽',
              style: Fonts.labelSmall,
            ),
          ]),
          SizedBox(width: 8.0),
          // PurchaseCardQuantity(),
          if (data?.document != null)
            IconButton(
              onPressed: () {
                context.router.push(
                  FilesRoute(
                    id: data?.document?.parentId,
                    idType: FileScreenTypeId.fromFolder.name,
                    isRoot: false,
                    department: Department.osivk.name,
                    projectName: data?.provisionName,
                  ),
                );
              },
              icon: SVG(Assets.icons.openFolder),
            )
        ],
      ),
    );
  }
}
