import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/index.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/features/purchase_list/domain/column.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/shared/data/models/search.dart';

part 'bloc.freezed.dart';
part 'events.dart';
part 'state.dart';

class BlocPurchaseList
    extends Bloc<BlocPurchaseListEvents, BlocPurchaseListState> {
  static const String _columnVisibilityKey = 'purchase_list_column_visibility';

  BlocPurchaseList()
      : super(
          BlocPurchaseListState(
            selecting: true,
            selectedProducts: {},
            productItems: ProvisionsListModel(),
            selectedFilter: ProvisionsFilter.cooperation,
            products: [],
            columnVisibility: _getDefaultColumnVisibility(),
            options: ProvisionsColumnOptionsOutput(),
          ),
        ) {
    _loadColumnVisibility();
    on<SetSelectedProducts>((event, emit) {
      emit(state.copyWith(selectedProducts: event.selectedProducts));
    });
    on<SetSelecting>((event, emit) {
      emit(state.copyWith(selecting: event.selecting));
    });
    on<ToggleSelecting>((event, emit) {
      emit(state.copyWith(selecting: !state.selecting));
    });
    on<SelectProvisionProduct>((event, emit) {
      final updatedProducts =
          Map<String, ProvisionItemModel>.from(state.selectedProducts);
      final id = event.product.id ?? event.product.uniqueId;
      if (id != null) {
        if (updatedProducts.containsKey(id)) {
          updatedProducts.remove(id);
        } else {
          updatedProducts[id] = event.product;
        }
      }
      emit(state.copyWith(selectedProducts: updatedProducts));
    });

    // Unified handler for provision product selection
    on<ToggleProvisionProductSelection>((event, emit) {
      // Handle selectedProducts (for lot operations)
      final updatedProducts =
          Map<String, ProvisionItemModel>.from(state.selectedProducts);
      final id = event.product.product?.id ?? event.product.uniqueId;
      if (id != null) {
        if (updatedProducts.containsKey(id)) {
          updatedProducts.remove(id);
        } else {
          // Convert ProvisionProductModel to ProvisionItemModel for storage
          final provisionItem = ProvisionItemModel(
            id: event.product.product?.id,
            uniqueId: event.product.uniqueId,
            product: event.product.product,
            material: event.product.material,
          );
          updatedProducts[id] = provisionItem;
        }
      }

      // Handle selectedProvisionProductsForTask (for task operations)
      final selectedProductIds = state.selectedProvisionProductsForTask
          .map((product) => product.product?.id ?? product.uniqueId)
          .where((id) => id != null)
          .toSet();

      final productId = event.product.product?.id ?? event.product.uniqueId;
      final isCurrentlySelected =
          productId != null && selectedProductIds.contains(productId);

      List<ProvisionProductModel> updatedProductsForTask;
      List<TaskProgressModel> updatedTasks =
          List.of(state.selectedProgressTasks);

      if (isCurrentlySelected) {
        // Remove product from selection
        updatedProductsForTask = List.of(state.selectedProvisionProductsForTask)
          ..removeWhere(
              (item) => (item.product?.id ?? item.uniqueId) == productId);

        // Also remove associated task if it exists
        if (event.product.task != null) {
          updatedTasks.removeWhere((task) => task.id == event.product.task!.id);
        }
      } else {
        // Add product to selection
        updatedProductsForTask = List.of(state.selectedProvisionProductsForTask)
          ..add(event.product);

        // Also add associated task if it exists and not already selected
        if (event.product.task != null &&
            !updatedTasks.any((task) => task.id == event.product.task!.id)) {
          updatedTasks.add(event.product.task!);
        }
      }

      emit(state.copyWith(
        selectedProducts: updatedProducts,
        selectedProvisionProductsForTask: updatedProductsForTask,
        selectedProgressTasks: updatedTasks,
      ));
    });

    on<ClearProvisionProductSelections>((event, emit) {
      emit(state.copyWith(
        selectedProducts: {},
        selectedProvisionProductsForTask: [],
        selectedProgressTasks: [],
      ));
    });

    // Handlers for task progress management
    on<ToggleSelectedProgressTaskInPurchaseList>((event, emit) {
      final selectedProgressTaskIds = state.selectedProgressTasks
          .map((progressTask) => progressTask.id)
          .where((id) => id != null)
          .toSet();

      final isCurrentlySelected =
          selectedProgressTaskIds.contains(event.item.id);

      if (isCurrentlySelected) {
        // Remove task from selection
        final updatedTasks = List.of(state.selectedProgressTasks)
          ..removeWhere((item) => item.id == event.item.id);

        // Also remove associated product if it exists
        List<ProvisionProductModel> updatedProducts =
            List.of(state.selectedProvisionProductsForTask);
        updatedProducts
            .removeWhere((product) => product.task?.id == event.item.id);

        emit(state.copyWith(
          selectedProgressTasks: updatedTasks,
          selectedProvisionProductsForTask: updatedProducts,
        ));
      } else {
        // Add task to selection
        final updatedTasks = List.of(state.selectedProgressTasks)
          ..add(event.item);

        // Also add associated product if it exists and not already selected
        List<ProvisionProductModel> updatedProducts =
            List.of(state.selectedProvisionProductsForTask);
        final associatedProduct = state.products.firstWhere(
          (product) => product.task?.id == event.item.id,
          orElse: () => ProvisionProductModel(),
        );

        if (associatedProduct.product?.id != null ||
            associatedProduct.uniqueId != null) {
          final productId =
              associatedProduct.product?.id ?? associatedProduct.uniqueId;
          final isProductAlreadySelected = updatedProducts.any(
            (product) => (product.product?.id ?? product.uniqueId) == productId,
          );

          if (!isProductAlreadySelected) {
            updatedProducts.add(associatedProduct);
          }
        }

        emit(state.copyWith(
          selectedProgressTasks: updatedTasks,
          selectedProvisionProductsForTask: updatedProducts,
        ));
      }
    });

    on<ClearProgressTasksSelectionsInPurchaseList>((event, emit) {
      emit(state.copyWith(
        selectedProgressTasks: [],
        selectedProvisionProductsForTask: [],
      ));
    });
    on<FetchPurchases>((event, emit) async {
      final materials =
          await PurchaseListRepository.getMaterials(projectId: event.projectId);
      emit(state.copyWith(
          productItems: materials.data ?? ProvisionsListModel()));
    });
    on<SetProvisionsFilter>((event, emit) {
      emit(state.copyWith(selectedFilter: event.filter));
    });
    on<SetProvisionProducts>((event, emit) {
      emit(state.copyWith(products: event.products));
    });
    on<UpdateColumnVisibility>((event, emit) async {
      final updatedVisibility = Map<String, bool>.from(event.columnVisibility)
        ..['2'] = true;
      emit(state.copyWith(columnVisibility: updatedVisibility));
      await _saveColumnVisibility(updatedVisibility);
    });
    on<ApplyFilters>((event, emit) async {
      emit(state.copyWith(searchFilters: event.filters, isFiltersOpen: false));
      add(LoadDataWithFilters());
    });
    on<ClearFilters>((event, emit) {
      final projectId = state.searchFilters?.projectId ?? '';
      emit(state.copyWith(
        searchFilters: SearchFiltersModel(
            projectId: projectId, filterType: state.selectedFilter),
      ));
      add(LoadDataWithFilters());
    });
    on<ToggleFiltersPanel>((event, emit) {
      emit(state.copyWith(isFiltersOpen: !state.isFiltersOpen));
    });
    on<LoadDataWithFilters>((event, emit) async {
      emit(state.copyWith(isLoading: true, error: null));
      try {
        final filters = state.searchFilters;
        if (filters == null) {
          emit(state.copyWith(isLoading: false, error: 'No filters applied'));
          return;
        }
        final response =
            await PurchaseListRepositoryV2.getRequirements(SearchModel(
          filters: filters,
          pagination:
              SearchPaginationModel(offset: event.offset, limit: event.limit),
        ));
        emit(state.copyWith(
          products: response.data?.items ?? [],
          totalItems: response.data?.totalItems ?? 0,
          isLoading: false,
        ));
      } catch (e) {
        emit(state.copyWith(isLoading: false, error: e.toString()));
      }
    });
    on<RemoveFilter>((event, emit) {
      if (state.searchFilters == null) return;
      final updatedFilters =
          state.searchFilters!.copyWithDynamic(event.filterKey, null);
      emit(state.copyWith(searchFilters: updatedFilters));
      add(LoadDataWithFilters());
    });
    on<FetchColumnOptions>((event, emit) async {
      emit(state.copyWith(isLoading: true, error: null));
      try {
        final response =
            await PurchaseListRepositoryV2.getColumnOptions(event.input);
        emit(state.copyWith(
            options: response.data ?? ProvisionsColumnOptionsOutput(),
            isLoading: false));
      } catch (e) {
        emit(state.copyWith(
            options: ProvisionsColumnOptionsOutput(),
            isLoading: false,
            error: e.toString()));
      }
    });
  }

  static Map<String, bool> _getDefaultColumnVisibility() {
    return {
      for (var i = 0; i < PurchaseListTableData.columns.length; i++)
        i.toString(): true
    };
  }

  Future<void> _loadColumnVisibility() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_columnVisibilityKey);
      if (jsonString != null) {
        final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
        final visibility = {
          for (var e in jsonMap.entries) e.key: e.value as bool
        };
        visibility['2'] = true; // Ensure "Name" column is always visible
        add(UpdateColumnVisibility(visibility));
      }
    } catch (e) {
      add(UpdateColumnVisibility(_getDefaultColumnVisibility()));
    }
  }

  Future<void> _saveColumnVisibility(Map<String, bool> visibility) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_columnVisibilityKey, json.encode(visibility));
    } catch (e) {
      // Handle silently or log if needed
    }
  }
}

// Extension to simplify dynamic filter removal
extension SearchFiltersModelExtension on SearchFiltersModel {
  SearchFiltersModel copyWithDynamic(String key, dynamic value) {
    final fields = {
      'query': copyWith(query: value),
      'branchId': copyWith(branchId: value),
      'department': copyWith(department: value),
      'documentDepartment': copyWith(documentDepartment: value),
      'projectIds': copyWith(projectIds: value),
      'status': copyWith(status: value),
      'userId': copyWith(userId: value),
      'productIds': copyWith(productIds: value),
      'root': copyWith(root: value),
      'type': copyWith(type: value),
      'parentProductId': copyWith(parentProductId: value),
      'roles': copyWith(roles: value),
      'productId': copyWith(productId: value),
      'bluePrint': copyWith(bluePrint: value),
      'isFolder': copyWith(isFolder: value),
      'extension': copyWith(extension: value),
      'parentFolderId': copyWith(parentFolderId: value),
      'version': copyWith(version: value),
      'materialId': copyWith(materialId: value),
      'storageType': copyWith(storageType: value),
      'materialType': copyWith(materialType: value),
      'baseUnit': copyWith(baseUnit: value),
      'visible': copyWith(visible: value),
      'remainingMaterials': copyWith(remainingMaterials: value),
      'workerId': copyWith(workerId: value),
      'taskStatus': copyWith(taskStatus: value),
      'fromDate': copyWith(fromDate: value),
      'toDate': copyWith(toDate: value),
      'clientType': copyWith(clientType: value),
      'provisionId': copyWith(provisionId: value),
      'deliveryStatus': copyWith(deliveryStatus: value),
      'filterType': copyWith(filterType: value),
      'dateFrom': copyWith(dateFrom: value),
      'dateTo': copyWith(dateTo: value),
      'drawingNumber': copyWith(drawingNumber: value),
      'drawingNumbers': copyWith(drawingNumbers: value),
      'name': copyWith(name: value),
      'names': copyWith(names: value),
      'material': copyWith(material: value),
      'materials': copyWith(materials: value),
      'feature': copyWith(feature: value),
      'features': copyWith(features: value),
      'massFrom': copyWith(massFrom: value),
      'massTo': copyWith(massTo: value),
      'quantityFrom': copyWith(quantityFrom: value),
      'quantityTo': copyWith(quantityTo: value),
      'requirement': copyWith(requirement: value),
      'requirements': copyWith(requirements: value),
      'materialRequirement': copyWith(materialRequirement: value),
      'materialRequirements': copyWith(materialRequirements: value),
      'priority': copyWith(priority: value),
      'priorities': copyWith(priorities: value),
      'responsiblePerson': copyWith(responsiblePerson: value),
      'responsiblePersons': copyWith(responsiblePersons: value),
      'taskDateFrom': copyWith(taskDateFrom: value),
      'taskDateTo': copyWith(taskDateTo: value),
      'supplyStatus': copyWith(supplyStatus: value),
      'plannedContractDateFrom': copyWith(plannedContractDateFrom: value),
      'plannedContractDateTo': copyWith(plannedContractDateTo: value),
      'lotNumbers': copyWith(lotNumbers: value),
      'lotNames': copyWith(lotNames: value),
      'contractNumber': copyWith(contractNumber: value),
      'contractNumbers': copyWith(contractNumbers: value),
      'supplier': copyWith(supplier: value),
      'suppliers': copyWith(suppliers: value),
      'deliveryDateFrom': copyWith(deliveryDateFrom: value),
      'deliveryDateTo': copyWith(deliveryDateTo: value),
      'costFrom': copyWith(costFrom: value),
      'costTo': copyWith(costTo: value),
      'lotIds': copyWith(lotIds: value),
      'contractIds': copyWith(contractIds: value),
      'parentName': copyWith(parentName: value),
      'parentNames': copyWith(parentNames: value),
    };
    return fields[key] ?? this;
  }
}
