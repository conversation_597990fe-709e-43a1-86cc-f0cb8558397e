# Интеграция деталей продуктов в контракты

## Обзор

Добавлена полная поддержка деталей продуктов (`ProductDetail`) в создание и редактирование контрактов. Теперь пользователи могут указывать детальную информацию для каждого продукта в контракте, включая цену, количество, единицы измерения, даты поставки и примечания.

## Обновления модели данных

### ProductDetail

Расширена модель `ProductDetail` с новыми полями:

```dart
@freezed
class ProductDetail with _$ProductDetail {
  factory ProductDetail({
    String? productId,        // ID продукта
    double? price,           // Цена (>= 0)
    String? unit,            // Единица измерения
    String? deliveryDate,    // Дата поставки (YYYY-MM-DD)
    double? quantity,        // Количество (>= 0)
    String? notes,           // Примечания
  }) = _ProductDetail;
}
```

**Новые поля:**
- `unit` - единица измерения (шт, кг, м, л и т.д.)
- `notes` - примечания к продукту

## Новый компонент: ProductDetailsSection

### Особенности

1. **Автоматическая инициализация**: Создает записи для всех выбранных продуктов
2. **Валидация**: Проверяет обязательные поля (цена, количество)
3. **Удобный интерфейс**: Карточки для каждого продукта с группировкой полей
4. **Выбор даты**: Встроенный date picker для дат поставки
5. **Реактивность**: Автоматическое обновление при изменении выбранных продуктов

### Структура интерфейса

Для каждого продукта отображается карточка с полями:

**Строка 1:**
- Цена (обязательно) + валюта (₽)
- Количество (обязательно)

**Строка 2:**
- Единица измерения (опционально)
- Дата поставки (опционально, с date picker)

**Строка 3:**
- Примечания (опционально, многострочное)

### Валидация

- **Цена**: Обязательное поле, должна быть >= 0
- **Количество**: Обязательное поле, должно быть > 0
- **Остальные поля**: Опциональные

## Интеграция в CreateContractBody

### Изменения в состоянии

```dart
class _CreateContractBodyState extends State<CreateContractBody> {
  // Добавлено состояние для деталей продуктов
  List<ProductDetail> _productDetails = [];
  
  // Инициализация при редактировании
  void _initializeFields() {
    if (widget.existingContract != null) {
      final contract = widget.existingContract!;
      
      // Загружаем существующие детали продуктов
      if (contract.productDetails != null && 
          contract.productDetails!.isNotEmpty) {
        _productDetails.clear();
        _productDetails.addAll(contract.productDetails!);
      }
    }
  }
}
```

### Размещение в UI

Компонент размещен между полем "Примечания" и выбором файлов:

```dart
// Примечания
TextFormField(controller: _notesController, ...),

const SizedBox(height: 16.0),

// Секция с деталями продуктов
ProductDetailsSection(
  selectedProductIds: widget.selectedProductIds,
  productDetails: _productDetails,
  onProductDetailsChanged: (details) {
    setState(() {
      _productDetails = details;
    });
  },
),

const SizedBox(height: 16.0),

// Выбор файлов
Row(children: [...]),
```

### Передача данных в API

Обновлены методы создания и обновления контрактов:

```dart
// Создание контракта
final contractInput = ProvisionCreateContractInput(
  // ... другие поля
  productDetails: _productDetails,  // Передаем детали продуктов
  paymentDetails: [],
);

// Обновление контракта
final updateInput = ProvisionUpdateContractInput(
  // ... другие поля
  productDetails: _productDetails,  // Передаем детали продуктов
  paymentDetails: [],
);
```

## Логика работы

### 1. Автоматическая инициализация

При открытии формы:
- Для **создания**: создаются пустые записи для всех выбранных продуктов
- Для **редактирования**: загружаются существующие детали из контракта

### 2. Синхронизация с выбранными продуктами

```dart
void _initializeProductDetails() {
  // Добавляем записи для новых продуктов
  for (final productId in widget.selectedProductIds) {
    if (!_productDetails.any((detail) => detail.productId == productId)) {
      _productDetails.add(ProductDetail(
        productId: productId,
        price: 0.0,
        quantity: 1.0,
        unit: 'шт',
      ));
    }
  }
  
  // Удаляем записи для неактуальных продуктов
  _productDetails.removeWhere(
    (detail) => !widget.selectedProductIds.contains(detail.productId),
  );
}
```

### 3. Валидация формы

Валидация происходит на уровне каждого поля:

```dart
validator: (value) {
  if (value == null || value.isEmpty) {
    return 'Укажите цену';
  }
  final price = double.tryParse(value);
  if (price == null || price < 0) {
    return 'Цена должна быть >= 0';
  }
  return null;
}
```

## Примеры использования

### Создание контракта с деталями продуктов

```dart
// Пользователь заполняет форму:
// Продукт 1: цена=1000₽, количество=5шт, единица=шт, дата=2024-01-15
// Продукт 2: цена=2500₽, количество=2шт, единица=кг, примечания="Особые требования"

final productDetails = [
  ProductDetail(
    productId: 'product_1',
    price: 1000.0,
    quantity: 5.0,
    unit: 'шт',
    deliveryDate: '2024-01-15',
  ),
  ProductDetail(
    productId: 'product_2',
    price: 2500.0,
    quantity: 2.0,
    unit: 'кг',
    notes: 'Особые требования',
  ),
];
```

### Редактирование существующего контракта

При открытии формы редактирования:
1. Загружаются существующие детали продуктов
2. Поля автоматически заполняются
3. Пользователь может изменить любые значения
4. При сохранении обновляются все детали

## Преимущества

1. **Детализация**: Полная информация о каждом продукте в контракте
2. **Гибкость**: Поддержка различных единиц измерения и дат поставки
3. **Удобство**: Интуитивный интерфейс с группировкой полей
4. **Валидация**: Предотвращение ошибок ввода
5. **Совместимость**: Работает как для создания, так и для редактирования
6. **Реактивность**: Автоматическая синхронизация с выбранными продуктами

## Файлы

- `product_details_section.dart` - Компонент для управления деталями продуктов
- `create_contract.dart` - Обновленная форма создания/редактирования контракта
- `index.dart` - Модель данных с расширенным ProductDetail
- `new.dart` - API методы с поддержкой productDetails

## Технические детails

- **Состояние**: Локальное состояние в `_CreateContractBodyState`
- **Валидация**: На уровне отдельных полей с немедленной обратной связью
- **Производительность**: Эффективное обновление только измененных элементов
- **Типобезопасность**: Полная поддержка типов Dart с freezed моделями
