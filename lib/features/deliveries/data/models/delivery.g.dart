// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeliveryGroupModelImpl _$$DeliveryGroupModelImplFromJson(
        Map<String, dynamic> json) =>
    _$DeliveryGroupModelImpl(
      expectedDate: json['expectedDate'] == null
          ? null
          : DateTime.parse(json['expectedDate'] as String),
      materials: (json['materials'] as List<dynamic>?)
          ?.map(
              (e) => DeliveryMaterialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$DeliveryGroupModelImplToJson(
        _$DeliveryGroupModelImpl instance) =>
    <String, dynamic>{
      if (instance.expectedDate?.toIso8601String() case final value?)
        'expectedDate': value,
      if (instance.materials case final value?) 'materials': value,
    };

_$DeliveryModelImpl _$$DeliveryModelImplFromJson(Map<String, dynamic> json) =>
    _$DeliveryModelImpl(
      id: json['_id'] as String?,
      provisionId: json['provisionId'] as String?,
      projectId: json['projectId'] as String?,
      status: $enumDecodeNullable(_$DeliveryStatusEnumMap, json['status']),
      supplier: json['supplier'] == null
          ? null
          : ClientModel.fromJson(json['supplier'] as Map<String, dynamic>),
      expectedDate: json['expectedDate'] == null
          ? null
          : DateTime.parse(json['expectedDate'] as String),
      deliveryDate: json['deliveryDate'] == null
          ? null
          : DateTime.parse(json['deliveryDate'] as String),
      items: (json['items'] as List<dynamic>?)
          ?.map(
              (e) => DeliveryMaterialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      comment: json['comment'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$DeliveryModelImplToJson(_$DeliveryModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.provisionId case final value?) 'provisionId': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (_$DeliveryStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.supplier case final value?) 'supplier': value,
      if (instance.expectedDate?.toIso8601String() case final value?)
        'expectedDate': value,
      if (instance.deliveryDate?.toIso8601String() case final value?)
        'deliveryDate': value,
      if (instance.items case final value?) 'items': value,
      if (instance.comment case final value?) 'comment': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

const _$DeliveryStatusEnumMap = {
  DeliveryStatus.pending: 'pending',
  DeliveryStatus.qcPending: 'qc_pending',
  DeliveryStatus.qcRejected: 'qc_rejected',
  DeliveryStatus.qcPartRejected: 'qc_part_rejected',
  DeliveryStatus.delivered: 'delivered',
  DeliveryStatus.cancelled: 'cancelled',
};

_$DeliveryMaterialModelImpl _$$DeliveryMaterialModelImplFromJson(
        Map<String, dynamic> json) =>
    _$DeliveryMaterialModelImpl(
      provisionItemId: json['provisionItemId'] as String?,
      quantityApproved: (json['quantityApproved'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num?)?.toDouble(),
      price: (json['price'] as num?)?.toDouble(),
      unitType: $enumDecodeNullable(_$UnitTypeEnumMap, json['unitType']),
      materialName: json['materialName'] as String?,
    );

Map<String, dynamic> _$$DeliveryMaterialModelImplToJson(
        _$DeliveryMaterialModelImpl instance) =>
    <String, dynamic>{
      if (instance.provisionItemId case final value?) 'provisionItemId': value,
      if (instance.quantityApproved case final value?)
        'quantityApproved': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.price case final value?) 'price': value,
      if (_$UnitTypeEnumMap[instance.unitType] case final value?)
        'unitType': value,
      if (instance.materialName case final value?) 'materialName': value,
    };

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};
