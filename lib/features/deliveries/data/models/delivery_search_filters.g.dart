// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_search_filters.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeliverySearchFiltersImpl _$$DeliverySearchFiltersImplFromJson(
        Map<String, dynamic> json) =>
    _$DeliverySearchFiltersImpl(
      dateFrom: json['dateFrom'] == null
          ? null
          : DateTime.parse(json['dateFrom'] as String),
      dateTo: json['dateTo'] == null
          ? null
          : DateTime.parse(json['dateTo'] as String),
      expectedDateFrom: json['expectedDateFrom'] == null
          ? null
          : DateTime.parse(json['expectedDateFrom'] as String),
      expectedDateTo: json['expectedDateTo'] == null
          ? null
          : DateTime.parse(json['expectedDateTo'] as String),
      deliveryDateFrom: json['deliveryDateFrom'] == null
          ? null
          : DateTime.parse(json['deliveryDateFrom'] as String),
      deliveryDateTo: json['deliveryDateTo'] == null
          ? null
          : DateTime.parse(json['deliveryDateTo'] as String),
      statuses: (json['statuses'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$DeliveryStatusEnumMap, e))
          .toList(),
      supplierId: json['supplierId'] as String?,
      supplierName: json['supplierName'] as String?,
      projectId: json['projectId'] as String,
      provisionId: json['provisionId'] as String?,
      materialName: json['materialName'] as String?,
      materialIds: (json['materialIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      quantityFrom: (json['quantityFrom'] as num?)?.toDouble(),
      quantityTo: (json['quantityTo'] as num?)?.toDouble(),
      priceFrom: (json['priceFrom'] as num?)?.toDouble(),
      priceTo: (json['priceTo'] as num?)?.toDouble(),
      comment: json['comment'] as String?,
      query: json['query'] as String?,
    );

Map<String, dynamic> _$$DeliverySearchFiltersImplToJson(
        _$DeliverySearchFiltersImpl instance) =>
    <String, dynamic>{
      'dateFrom': instance.dateFrom?.toIso8601String(),
      'dateTo': instance.dateTo?.toIso8601String(),
      'expectedDateFrom': instance.expectedDateFrom?.toIso8601String(),
      'expectedDateTo': instance.expectedDateTo?.toIso8601String(),
      'deliveryDateFrom': instance.deliveryDateFrom?.toIso8601String(),
      'deliveryDateTo': instance.deliveryDateTo?.toIso8601String(),
      'statuses':
          instance.statuses?.map((e) => _$DeliveryStatusEnumMap[e]!).toList(),
      'supplierId': instance.supplierId,
      'supplierName': instance.supplierName,
      'projectId': instance.projectId,
      'provisionId': instance.provisionId,
      'materialName': instance.materialName,
      'materialIds': instance.materialIds,
      'quantityFrom': instance.quantityFrom,
      'quantityTo': instance.quantityTo,
      'priceFrom': instance.priceFrom,
      'priceTo': instance.priceTo,
      'comment': instance.comment,
      'query': instance.query,
    };

const _$DeliveryStatusEnumMap = {
  DeliveryStatus.pending: 'pending',
  DeliveryStatus.qcPending: 'qc_pending',
  DeliveryStatus.qcRejected: 'qc_rejected',
  DeliveryStatus.qcPartRejected: 'qc_part_rejected',
  DeliveryStatus.delivered: 'delivered',
  DeliveryStatus.cancelled: 'cancelled',
};
