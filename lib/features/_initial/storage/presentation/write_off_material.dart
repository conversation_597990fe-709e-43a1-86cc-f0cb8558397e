import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/repositories/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/dropdown_button.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class WriteOffMaterialBody extends StatefulWidget {
  const WriteOffMaterialBody({
    super.key,
    required this.material,
    this.refresher,
  });

  final MaterialModel material;
  final VoidCallback? refresher;

  @override
  State<WriteOffMaterialBody> createState() => _WriteOffMaterialBodyState();
}

class _WriteOffMaterialBodyState extends State<WriteOffMaterialBody> {
  final TextEditingController _quantityController = TextEditingController();
  UnitType _selectedUnitType = UnitType.kg;
  List<UnitModel> _materialUnits = [];
  bool _dropDownIsOpened = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _recalculateQuantity(UnitType oldType, UnitType newType) {
    final oldIndex =
        _materialUnits.indexWhere((altUnit) => altUnit.unit == oldType);
    final newIndex =
        _materialUnits.indexWhere((altUnit) => altUnit.unit == newType);

    if (oldIndex >= 0 && newIndex >= 0) {
      final quantity = double.tryParse(_quantityController.text) ?? 1.0;
      final oldRatio = _materialUnits[oldIndex].ratio ?? 1.0;
      final newRatio = _materialUnits[newIndex].ratio ?? 1.0;

      final newQuantity = (quantity * newRatio) / oldRatio;

      _quantityController.text = newQuantity.toStringAsFixed(2);
    }
  }

  void _initialize() {
    if (widget.material.quantity != null) {
      _quantityController.text = '0.0';
      _selectedUnitType = widget.material.nomenclature?.baseUnit ?? UnitType.kg;
      _materialUnits = [
        UnitModel(
          unit: widget.material.nomenclature?.baseUnit ?? UnitType.kg,
          ratio: 1.0,
        ),
        ...(widget.material.nomenclature?.alternativeUnits ?? []),
      ];
    }
  }

  Future<void> _writeOffMaterial() async {
    final quantity = double.tryParse(_quantityController.text);
    if (quantity == null || widget.material.id == null) return;

    setState(() => _isLoading = true);

    try {
      final result = await StorageRepository.writeOff(
        widget.material.id!,
        _selectedUnitType,
        quantity,
      );
      Logger().d(result?.data);
      widget.refresher?.call();
      CustomDrawer.instance.hide();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(
      children: [
        if (_isLoading) LoadingIndicator(isDarkTheme: isDarkTheme),
        Column(
          children: [
            Expanded(
              child: ContentSection(
                quantityController: _quantityController,
                material: widget.material,
                selectedUnitType: _selectedUnitType,
                dropDownIsOpened: _dropDownIsOpened,
                materialUnits: _materialUnits,
                onUnitTypeChanged: (unitType, isOpened) {
                  _recalculateQuantity(_selectedUnitType, unitType);
                  setState(() {
                    _selectedUnitType = unitType;
                    _dropDownIsOpened = isOpened;
                  });
                },
              ),
            ),
            SaveButton(onPressed: _writeOffMaterial),
          ],
        ),
      ],
    );
  }
}

class LoadingIndicator extends StatelessWidget {
  const LoadingIndicator({super.key, required this.isDarkTheme});

  final bool isDarkTheme;

  @override
  Widget build(BuildContext context) {
    return LinearProgressIndicator(
      minHeight: 2.0,
      valueColor: AlwaysStoppedAnimation(
        isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
      ),
    );
  }
}

class ContentSection extends StatelessWidget {
  const ContentSection({
    super.key,
    required this.quantityController,
    required this.material,
    required this.selectedUnitType,
    required this.dropDownIsOpened,
    required this.onUnitTypeChanged,
    required this.materialUnits,
  });

  final TextEditingController quantityController;
  final MaterialModel material;
  final UnitType selectedUnitType;
  final bool dropDownIsOpened;
  final void Function(UnitType, bool) onUnitTypeChanged;
  final List<UnitModel> materialUnits;

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(20.0),
      children: [
        Text(
          'Списание материала',
          style: Fonts.titleMedium.merge(const TextStyle(height: 1.6)),
        ),
        const SizedBox(height: 24.0),
        QuantityInputSection(
          quantityController: quantityController,
          material: material,
          selectedUnitType: selectedUnitType,
          dropDownIsOpened: dropDownIsOpened,
          onUnitTypeChanged: onUnitTypeChanged,
          materialUnits: materialUnits,
        ),
      ],
    );
  }
}

class QuantityInputSection extends StatelessWidget {
  const QuantityInputSection({
    super.key,
    required this.quantityController,
    required this.material,
    required this.selectedUnitType,
    required this.dropDownIsOpened,
    required this.onUnitTypeChanged,
    required this.materialUnits,
  });

  final TextEditingController quantityController;
  final MaterialModel material;
  final UnitType selectedUnitType;
  final bool dropDownIsOpened;
  final void Function(UnitType, bool) onUnitTypeChanged;
  final List<UnitModel> materialUnits;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Какое количество списать?',
          style: Fonts.bodyMedium.merge(const TextStyle(height: 1.5)),
        ),
        const SizedBox(height: 10.0),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: quantityController,
                style: Fonts.labelSmall,
                decoration: const InputDecoration(hintText: 'количество'),
              ),
            ),
            const SizedBox(width: 8.0),
            UnitTypeDropdown(
              alternativeUnits: materialUnits,
              selectedUnitType: selectedUnitType,
              dropDownIsOpened: dropDownIsOpened,
              onUnitTypeChanged: onUnitTypeChanged,
              baseUnitType: material.nomenclature?.baseUnit ?? UnitType.kg,
            ),
          ],
        ),
      ],
    );
  }
}

class UnitTypeDropdown extends StatelessWidget {
  const UnitTypeDropdown({
    super.key,
    required this.alternativeUnits,
    required this.selectedUnitType,
    required this.dropDownIsOpened,
    required this.onUnitTypeChanged,
    required this.baseUnitType,
  });

  final List<UnitModel> alternativeUnits;
  final UnitType selectedUnitType;
  final bool dropDownIsOpened;
  final void Function(UnitType, bool) onUnitTypeChanged;
  final UnitType baseUnitType;

  @override
  Widget build(BuildContext context) {
    return CustomDropDownButton(
      isOpened: dropDownIsOpened,
      openable: alternativeUnits.isNotEmpty,
      setIsOpened: (value) => onUnitTypeChanged(selectedUnitType, value),
      text: selectedUnitType.getName(),
      children: alternativeUnits.map((unit) {
        final type = unit.unit;

        return CustomDropdownMenuItem(
          text: type?.getName(),
          onTap: () {
            onUnitTypeChanged(type ?? UnitType.kg, false);
            CustomDropdownMenu.instance.hide();
          },
        );
      }).toList(),
    );
  }
}

class SaveButton extends StatelessWidget {
  const SaveButton({super.key, required this.onPressed});

  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: CustomElevatedButton(
        type: CustomElevatedButtonTypes.attention,
        onPressed: onPressed,
        text: 'Списать',
      ),
    );
  }
}
