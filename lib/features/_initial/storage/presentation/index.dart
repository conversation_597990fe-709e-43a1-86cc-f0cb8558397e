import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/repositories/index.dart';
import 'package:sphere/features/_initial/storage/presentation/add_material.dart';
import 'package:sphere/features/_initial/storage/presentation/edit_material.dart';
import 'package:sphere/features/_initial/storage/presentation/material_card.dart';
import 'package:sphere/features/_initial/storage/presentation/transfer.dart';
import 'package:sphere/features/_initial/storage/presentation/write_off_material.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class StorageScreen extends StatefulWidget {
  const StorageScreen({super.key});

  @override
  State<StorageScreen> createState() => _StorageScreenState();
}

class _StorageScreenState extends State<StorageScreen>
    with AutoRouteAwareStateMixin<StorageScreen>, TickerProviderStateMixin {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();
  bool _isLoading = false;
  List<MaterialModel> _materials = [];
  bool _withProjects = false;

  final _debouncer = Debouncer();

  Future<void> _loadData() async {
    final searchQuery =
        _searchController.text.isNotEmpty ? _searchController.text : null;
    setState(() {
      _isLoading = true;
    });

    final result = await StorageRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: searchQuery,
        storageType: _withProjects ? StorageType.project : StorageType.common,
      ),
    ));
    final items = result?.data?.items;
    // await Future.delayed(const Duration(milliseconds: 20000));

    setState(() {
      _materials = items ?? [];
      _isLoading = false;
    });
  }

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    await _loadData();
    await _setAppBarConfig();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    await _loadData();
    await _setAppBarConfig();
  }

  Future<void> _setAppBarConfig() async {
    final newConfig = CustomAppBarConfig(
      title: 'Общий склад',
      description: 'Список всех материалов',
      rightPart: CustomAppBarFeatures.getPopupMenu(
        context: context,
        children: [
          CustomDropdownMenuItem(
            onTap: () {
              CustomDropdownMenu.instance.hide();
              CustomDrawer.instance.show(
                context: context,
                vsync: this,
                child: AddMaterialBody(
                  refresher: _loadData,
                ),
              );
            },
            icon: Assets.icons.add,
            text: 'Добавить материал',
          ),
          CustomDropdownMenuItem(
            onTap: () {
              _loadData();
            },
            icon: Assets.icons.repeat,
            text: 'Обновить',
          ),
        ],
      ),
      isLoading: _isLoading,
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 700;
    final isLarge = MediaQuery.of(context).size.width >= 1200;
    // final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Column(children: [
      Padding(
          padding: EdgeInsets.all(12),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  onChanged: (value) {
                    _debouncer.run(_loadData);
                  },
                  style: Fonts.labelSmall,
                  decoration: InputDecoration(
                    hintText: 'Поиск',
                    suffixIcon: Align(
                      widthFactor: 1.0,
                      alignment: Alignment.center,
                      child: SVG(
                        Assets.icons.search,
                        color: AppColors.medium,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12.0),
              SVG(Assets.icons.openFolder),
              SizedBox(width: 4.0),
              Switch.adaptive(
                  value: _withProjects,
                  onChanged: ((value) {
                    setState(() {
                      _withProjects = value;
                    });
                    _loadData();
                  })),
              SizedBox(height: 24.0, child: VerticalDivider(width: 24.0)),
              CustomElevatedButton(
                onPressed: () {
                  context.router.push(DeliveriesRoute());
                },
                text: 'Список поставок',
                style: Fonts.labelSmall,
              ),
            ],
          )),
      Expanded(
        child: MasonryGridView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.fromLTRB(12.0, 0.0, 12.0, 32.0),
          gridDelegate: SliverSimpleGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: isDesktop
                ? isLarge
                    ? 3
                    : 2
                : 1,
          ),
          mainAxisSpacing: 12.0,
          crossAxisSpacing: 12.0,
          itemCount: _materials.isNotEmpty
              ? _materials.length
              : _isLoading
                  ? 6
                  : 0,
          itemBuilder: (context, index) {
            final material =
                _materials.isNotEmpty ? _materials[index] : MaterialModel();

            return MaterialCard(
              withProjectName: true,
              isLoading: _isLoading,
              material: material,
              onSecondaryTapDown: (details) {
                CustomDropdownMenu.instance.hide();
                CustomDropdownMenu.instance.show(
                  context: context,
                  items: [
                    CustomDropdownMenuItem(
                      icon: Assets.icons.edit,
                      text: 'Редактировать',
                      description: 'Корректировка значений',
                      onTap: () {
                        CustomDropdownMenu.instance.hide();
                        CustomDrawer.instance.show(
                          context: context,
                          vsync: this,
                          child: EditMaterialBody(
                            material: material,
                            refresher: _loadData,
                          ),
                        );
                      },
                    ),
                    CustomDropdownMenuItem(
                      icon: Assets.icons.motion,
                      text: 'Переместить',
                      onTap: () {
                        CustomDropdownMenu.instance.hide();
                        CustomDrawer.instance.show(
                          context: context,
                          vsync: this,
                          child: TransferMaterialBody(
                            material: material,
                            refresher: _loadData,
                          ),
                        );
                      },
                    ),
                    CustomDropdownMenuItem(
                      icon: Assets.icons.delete,
                      text: 'Списать',
                      onTap: () {
                        CustomDropdownMenu.instance.hide();
                        CustomDrawer.instance.show(
                          context: context,
                          vsync: this,
                          child: WriteOffMaterialBody(
                            material: material,
                            refresher: _loadData,
                          ),
                        );
                      },
                    ),
                  ],
                  position: details.globalPosition,
                );
              },
              onTap: () {
                // context.router.push(ProjectRoute(id: project.id ?? '0'));
              },
            );
          },
        ),
      ),
    ]);
  }
}
