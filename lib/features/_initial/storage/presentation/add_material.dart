import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/nomenclatures/data/repositories/index.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class AddMaterialBody extends StatefulWidget {
  const AddMaterialBody({super.key, this.refresher});

  final Function()? refresher;

  @override
  State<AddMaterialBody> createState() => _AddMaterialBodyState();
}

class _AddMaterialBodyState extends State<AddMaterialBody> {
  final _nomenclatureController = TextEditingController(text: '');
  final _quantityController = TextEditingController(text: '');
  List<NomenclatureModel> _nomenclatures = [];
  NomenclatureModel? _selectedNomenclature;
  UnitType _selectedUnitType = UnitType.kg;
  bool _isLoading = false;

  final _debouncer = Debouncer();

  Future<void> _getNomenclatures() async {
    if (_nomenclatureController.text.isEmpty) return;
    setState(() {
      _isLoading = true;
    });
    final result = await NomenclaturesRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _nomenclatureController.text,
      ),
    ));

    if (result?.data != null && result!.data?.items != null) {
      final nomenclatures = result.data!.items!;
      setState(() {
        _nomenclatures = [
          ...nomenclatures,
        ];
        _isLoading = false;
      });
    }
  }

  void _addMaterial() async {
    final quantity = double.tryParse(_quantityController.text);
    if (_selectedNomenclature?.id == null) return;
    if (quantity == null) return;

    setState(() {
      _isLoading = true;
    });

    final result = await StorageRepository.add(MaterialModel(
      nomenclatureId: _selectedNomenclature!.id!,
      quantity: quantity,
      unit: _selectedUnitType,
      status: 'ready',
    ));
    final logger = Logger();
    logger.d(result?.data);

    setState(() {
      _isLoading = false;
    });
    widget.refresher?.call();
  }

  void _onSelectNomenclature(NomenclatureModel nomenclature) {
    setState(() {
      _selectedNomenclature = nomenclature;
      _nomenclatureController.text = nomenclature.name ?? '';
      _nomenclatures = [];
    });
    // widget.refresher?.call();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    _selectedUnitType = _selectedNomenclature?.baseUnit ?? UnitType.kg;

    return Stack(
      children: [
        if (_isLoading)
          LinearProgressIndicator(
            minHeight: 2.0,
            borderRadius: BorderRadius.circular(20.0),
            color: isDarkTheme
                ? AppColors.darkSecondary
                : AppColors.lightSecondary,
          ),
        Column(
          children: [
            Expanded(
              child: ListView(padding: const EdgeInsets.all(20.0), children: [
                Text(
                  'Добавление материала',
                  style: Fonts.titleMedium.merge(
                    const TextStyle(height: 1.6),
                  ),
                ),
                const SizedBox(height: 10.0),
                Text(
                  'Какая номенклатура?',
                  style: Fonts.bodyMedium.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                TextField(
                  controller: _nomenclatureController,
                  style: Fonts.labelSmall,
                  onChanged: (value) {
                    setState(() {
                      _selectedNomenclature = null;
                    });
                    if (value.isEmpty) {
                      setState(() {
                        _nomenclatures.clear();
                      });
                    }
                    if (value.isNotEmpty) _debouncer.run(_getNomenclatures);
                  },
                  decoration: const InputDecoration(
                    hintText: 'Имя номенклатуры',
                  ),
                ),
                // if (_selectedNomenclature == null) const SizedBox(height: 6.0),
                // if (_selectedNomenclature == null)
                //   Text(
                //     'Если вы не выбрали номенклатуру из списка, она будет создана и применена к этому материалу',
                //     style: Fonts.bodySmall.merge(
                //       const TextStyle(
                //         height: 1.5,
                //         color: AppColors.medium,
                //       ),
                //     ),
                //   ),
                if (_nomenclatures.isNotEmpty || _selectedNomenclature != null)
                  const SizedBox(height: 10.0),
                if (_selectedNomenclature != null)
                  Row(children: [
                    CustomChip(
                      selected: true,
                      text: _selectedNomenclature!.name,
                      onTap: () {
                        setState(() {
                          _selectedNomenclature = null;
                          _nomenclatureController.text = '';
                        });
                      },
                    ),
                  ]),
                if (_selectedNomenclature != null) const SizedBox(height: 8.0),
                if (_nomenclatures.isNotEmpty)
                  Wrap(
                    spacing: 6.0,
                    runSpacing: 6.0,
                    children: _nomenclatures
                        .where((client) => client != _selectedNomenclature)
                        .map((client) {
                      return CustomChip(
                        selected: _selectedNomenclature == client,
                        text: client.name,
                        onTap: () => _onSelectNomenclature(client),
                      );
                    }).toList(),
                  ),
                const SizedBox(height: 24.0),
                Text(
                  'Какое количество?',
                  style: Fonts.bodyMedium.merge(
                    const TextStyle(height: 1.5),
                  ),
                ),
                const SizedBox(height: 10.0),
                Row(children: [
                  Expanded(
                    child: TextField(
                      controller: _quantityController,
                      style: Fonts.labelSmall,
                      decoration: const InputDecoration(
                        hintText: 'количество',
                      ),
                    ),
                  ),
                  const SizedBox(width: 12.0),
                  Text(
                    _selectedNomenclature?.baseUnit?.getName() ?? 'кг',
                    style: Fonts.bodyMedium.merge(
                      TextStyle(
                        color: isDarkTheme
                            ? AppColors.darkDescription
                            : AppColors.lightDescription,
                      ),
                    ),
                  ),
                ])
              ]),
            ),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: CustomElevatedButton(
                type: CustomElevatedButtonTypes.accent,
                onPressed: _addMaterial,
                text: 'Добавить материал',
              ),
            ),
          ],
        ),
      ],
    );
  }
}
