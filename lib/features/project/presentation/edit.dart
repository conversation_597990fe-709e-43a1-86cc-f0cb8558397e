import 'package:flutter/material.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/client/data/models/client.dart';
import 'package:sphere/features/client/data/repositories/index.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/models/view.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class ProjectEdit extends StatefulWidget {
  const ProjectEdit({super.key, this.project, this.onSave});

  final ProjectModel? project;
  final void Function(ProjectViewModel)? onSave;

  @override
  State<ProjectEdit> createState() => _ProjectEditState();
}

class _ProjectEditState extends State<ProjectEdit> {
  final _nameController = TextEditingController();
  final _numberController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _clientController = TextEditingController();
  ClientModel _selectedClient = const ClientModel();
  List<ClientModel> _clients = [];
  // String _headUserId = '';
  DateTime _releaseDate = DateTime.now();
  bool _calendarIsVisible = false;

  ProjectViewModel _project = ProjectViewModel();

  bool _isLoading = false;
  Future<void> _getClients() async {
    if (_clientController.text.isEmpty) return;
    setState(() {
      _isLoading = true;
    });
    final result = await ClientRepository.search(SearchModel(
      filters: SearchFiltersModel(
        query: _clientController.text,
      ),
    ));

    if (result?.data != null && result!.data?.items != null) {
      final clients = result.data!.items!;
      setState(() {
        _clients = [
          ...clients,
        ];
        _isLoading = false;
      });
    }
  }

  void _onSelectClient(ClientModel client) {
    setState(() {
      _selectedClient = client;
      _clientController.text = client.name ?? '';
      _clients = [];
    });
    // widget.refresher?.call();
  }

  Future<void> _save() async {
    if (widget.project?.id == null) return;
    setState(() {
      _isLoading = true;
    });

    final result = await ProjectRepository.edit(
      projectId: widget.project!.id!,
      name: _nameController.text,
      number: _numberController.text,
      description: _descriptionController.text,

      clientId: _selectedClient.id,
      clientName: _selectedClient.id == null ? _clientController.text : null,
      releaseDate: _releaseDate,
      // headUserId: '',
    );

    setState(() {
      _project = result?.data ?? ProjectViewModel();
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    if (widget.project != null) {
      _nameController.text = widget.project!.name ?? '';
      _numberController.text = widget.project!.number ?? '';
      _descriptionController.text = widget.project!.description ?? '';
      _selectedClient = widget.project!.client ?? const ClientModel();
      _releaseDate = widget.project!.releaseDate ?? DateTime.now();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(children: [
        Expanded(
          child: ListView(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0.0),
            children: [
              Text(
                'Редактирование проекта',
                style: Fonts.titleMedium.merge(
                  const TextStyle(height: 1.6),
                ),
              ),
              const SizedBox(height: 10.0),
              Text(
                'Название проекта',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 10.0),
              TextField(
                controller: _nameController,
                style: Fonts.labelSmall,
                decoration: const InputDecoration(
                  hintText: 'Имя',
                ),
              ),
              const SizedBox(height: 24.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Реализация проекта:',
                    style: Fonts.labelSmall,
                  ),
                  GhostButton(
                    onTap: () async {
                      setState(() {
                        _calendarIsVisible = !_calendarIsVisible;
                      });
                    },
                    child: Text(
                      getDateString(_releaseDate),
                      style: Fonts.labelSmall.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkSecondary
                              : AppColors.lightSecondary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (_calendarIsVisible) SizedBox(height: 8.0),
              if (_calendarIsVisible) Divider(height: 1.0),
              if (_calendarIsVisible)
                CalendarDatePicker(
                  initialDate: _releaseDate,
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(Duration(days: 730)),
                  currentDate: _releaseDate,
                  onDateChanged: (DateTime newDate) {
                    setState(() {
                      _releaseDate = newDate;
                      _calendarIsVisible = false;
                    });
                  },
                ),
              if (_calendarIsVisible) Divider(height: 1.0),
              const SizedBox(height: 24.0),
              Text(
                'Номер проекта',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 10.0),
              TextField(
                controller: _numberController,
                style: Fonts.labelSmall,
                decoration: const InputDecoration(
                  hintText: 'Номер',
                ),
              ),
              const SizedBox(height: 24.0),
              Text(
                'Какой клиент?',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 10.0),
              TextField(
                controller: _clientController,
                style: Fonts.labelSmall,
                onChanged: (value) {
                  setState(() {
                    _selectedClient =
                        widget.project?.client ?? const ClientModel();
                  });
                  _getClients();
                },
                decoration: const InputDecoration(
                  hintText: 'Имя клиента',
                ),
              ),
              if (_clients.isNotEmpty || _selectedClient.name != null)
                const SizedBox(height: 10.0),
              Row(children: [
                CustomChip(
                  selected: true,
                  text: _selectedClient.name,
                  onTap: () {
                    setState(() {
                      _selectedClient =
                          widget.project?.client ?? const ClientModel();
                      _clientController.text = '';
                    });
                  },
                ),
              ]),
              const SizedBox(height: 8.0),
              if (_clients.isNotEmpty)
                Wrap(
                  spacing: 6.0,
                  runSpacing: 6.0,
                  children: _clients
                      .where((client) => client != _selectedClient)
                      .map((client) {
                    return CustomChip(
                      selected: _selectedClient == client,
                      text: client.name,
                      onTap: () => _onSelectClient(client),
                    );
                  }).toList(),
                ),
              const SizedBox(height: 24.0),
              Text(
                'Описание проекта',
                style: Fonts.labelSmall.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 10.0),
              TextField(
                controller: _descriptionController,
                style: Fonts.labelSmall,
                decoration: const InputDecoration(
                  hintText: 'Описание',
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () async {
              await _save();
              widget.onSave?.call(_project);
            },
            text: 'Сохранить',
          ),
        ),
      ]),
    ]);
  }
}
