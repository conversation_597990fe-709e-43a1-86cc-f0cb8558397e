import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class ProjectViewWorkersDrawer extends StatefulWidget {
  const ProjectViewWorkersDrawer({
    super.key,
    this.projectId,
  });

  final String? projectId;

  @override
  State<ProjectViewWorkersDrawer> createState() =>
      _ProjectViewWorkersDrawerState();
}

class _ProjectViewWorkersDrawerState extends State<ProjectViewWorkersDrawer> {
  final ScrollController _scrollController = ScrollController();
  bool _showTopGradient = false;
  bool _showBottomGradient = true;
  List<UserModel> _users = [
    const UserModel(),
    const UserModel(),
    const UserModel(),
  ];
  bool _isLoading = false;
  final _searchController = TextEditingController(text: '');

  final _debouncer = Debouncer();

  void _loadUsers() async {
    setState(() {
      _isLoading = true;
    });
    final result = await UserRepository.search(SearchModel(
      filters: SearchFiltersModel(
        // TODO: add filters
        // department: Department.ogk,
        query: _searchController.text.isEmpty ? null : _searchController.text,
        projectId: widget.projectId,
        roles: [UserRole.headmanager, UserRole.manager],
      ),
    ));

    setState(() {
      if (result?.data != null && result!.data?.items != null) {
        final users = result.data!.items!;
        _users = users;
      }
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
    _loadUsers();
  }

  void _handleScroll() {
    setState(() {
      _showTopGradient = _scrollController.offset > 0;
      _showBottomGradient = _scrollController.position.pixels <
          _scrollController.position.maxScrollExtent;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0),
            child: Text(
              'Исполнители проекта (${_users.length})',
              style: Fonts.titleMedium.merge(
                const TextStyle(height: 1.6),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: TextField(
              controller: _searchController,
              onChanged: (_) {
                _debouncer.run(_loadUsers);
              },
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                hintText: 'Поиск',
                suffixIcon: Align(
                  widthFactor: 1.0,
                  alignment: Alignment.center,
                  child: SVG(
                    Assets.icons.search,
                    color: AppColors.medium,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: ShaderMask(
              shaderCallback: (Rect rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    if (_showTopGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                    isDarkTheme
                        ? AppColors.darkBackground.withOpacity(0)
                        : AppColors.lightBackground.withOpacity(0),
                    isDarkTheme
                        ? AppColors.darkBackground.withOpacity(0)
                        : AppColors.lightBackground.withOpacity(0),
                    if (_showBottomGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                  ],
                  stops: const [0.0, 0.05, 0.95, 1.0],
                ).createShader(rect);
              },
              blendMode: BlendMode.dstOut,
              child: ListView(
                controller: _scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                children: [
                  const SizedBox(height: 12.0),
                  ..._users.asMap().entries.map((entry) {
                    final user = entry.value;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        UserCard(
                          isLoading: _isLoading,
                          // onTap: () {
                          //   _onSelectUser(user);
                          // },
                          user: user,
                        ),
                        const SizedBox(height: 12.0),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    ]);
  }
}
