import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/features/project/data/models/view.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/features/user/data/models/role.dart';
import 'package:sphere/features/user/data/models/user.dart';
import 'package:sphere/features/user/data/repositories/index.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class ProjectRemoveWorkersDrawer extends StatefulWidget {
  const ProjectRemoveWorkersDrawer({
    super.key,
    this.projectId,
    this.onSuccess,
  });

  final String? projectId;
  final void Function(ProjectViewModel)? onSuccess;

  @override
  State<ProjectRemoveWorkersDrawer> createState() =>
      _ProjectRemoveWorkersDrawerState();
}

class _ProjectRemoveWorkersDrawerState
    extends State<ProjectRemoveWorkersDrawer> {
  final ScrollController _scrollController = ScrollController();
  bool _showTopGradient = false;
  bool _showBottomGradient = true;
  List<UserModel> _users = [
    const UserModel(),
    const UserModel(),
    const UserModel(),
  ];
  final List<UserModel> _selectedUsers = [];
  bool _isLoading = false;
  final _searchController = TextEditingController(text: '');

  final _debouncer = Debouncer();

  void _loadUsers() async {
    setState(() {
      _isLoading = true;
    });
    final result = await UserRepository.search(SearchModel(
      filters: SearchFiltersModel(
        // TODO: add filters
        // department: Department.ogk,
        query: _searchController.text.isEmpty ? null : _searchController.text,
        projectId: widget.projectId,
        roles: [UserRole.headmanager, UserRole.manager],
      ),
    ));

    setState(() {
      if (result?.data != null && result!.data?.items != null) {
        final users = result.data!.items!;
        _users = users;
      }
      _isLoading = false;
    });
  }

  void _onSelectUser(UserModel user) {
    setState(() {
      if (_selectedUsers.contains(user)) {
        _selectedUsers.remove(user);
      } else {
        _selectedUsers.add(user);
      }
    });
  }

  void _removeSelectedUsers() async {
    setState(() {
      _isLoading = true;
    });
    if (widget.projectId == null) {
      print('project id is not found');
      return;
    }
    final selectedUsersIds = _selectedUsers
        .where((user) => user.id != null)
        .map((user) => user.id!)
        .toList();

    final result = await ProjectRepository.removeManagers(
      widget.projectId!,
      selectedUsersIds,
    );
    if (result?.data == null) return;
    widget.onSuccess?.call(result!.data!);

    setState(() {
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
    _loadUsers();
  }

  void _handleScroll() {
    setState(() {
      _showTopGradient = _scrollController.offset > 0;
      _showBottomGradient = _scrollController.position.pixels <
          _scrollController.position.maxScrollExtent;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Stack(children: [
      if (_isLoading)
        LinearProgressIndicator(
          minHeight: 2.0,
          borderRadius: BorderRadius.circular(20.0),
          color:
              isDarkTheme ? AppColors.darkSecondary : AppColors.lightSecondary,
        ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20.0, 20.0, 20.0, 0),
            child: Text(
              'Удаление исполнителей',
              style: Fonts.titleMedium.merge(
                const TextStyle(height: 1.6),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: TextField(
              controller: _searchController,
              onChanged: (_) {
                _debouncer.run(_loadUsers);
              },
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                hintText: 'Поиск',
                suffixIcon: Align(
                  widthFactor: 1.0,
                  alignment: Alignment.center,
                  child: SVG(
                    Assets.icons.search,
                    color: AppColors.medium,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: ShaderMask(
              shaderCallback: (Rect rect) {
                return LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    if (_showTopGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                    isDarkTheme
                        ? AppColors.darkBackground.withOpacity(0)
                        : AppColors.lightBackground.withOpacity(0),
                    isDarkTheme
                        ? AppColors.darkBackground.withOpacity(0)
                        : AppColors.lightBackground.withOpacity(0),
                    if (_showBottomGradient)
                      (isDarkTheme
                          ? AppColors.darkBackground
                          : AppColors.lightBackground)
                    else
                      Colors.transparent,
                  ],
                  stops: const [0.0, 0.05, 0.95, 1.0],
                ).createShader(rect);
              },
              blendMode: BlendMode.dstOut,
              child: ListView(
                controller: _scrollController,
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                children: [
                  const SizedBox(height: 12.0),
                  ..._selectedUsers.asMap().entries.map((entry) {
                    final user = entry.value;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        UserCard(
                          selected: true,
                          isLoading: _isLoading,
                          onTap: () {
                            _onSelectUser(user);
                          },
                          user: user,
                        ),
                        const SizedBox(height: 12.0),
                      ],
                    );
                  }),
                  if (_selectedUsers.isNotEmpty) const Divider(height: 1.0),
                  if (_selectedUsers.isNotEmpty) const SizedBox(height: 12.0),
                  ..._users
                      .where((user) => !_selectedUsers.contains(user))
                      .toList()
                      .asMap()
                      .entries
                      .map((entry) {
                    final user = entry.value;

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        UserCard(
                          isLoading: _isLoading,
                          onTap: () {
                            _onSelectUser(user);
                          },
                          user: user,
                        ),
                        const SizedBox(height: 12.0),
                      ],
                    );
                  }),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: CustomElevatedButton(
              type: CustomElevatedButtonTypes.attention,
              onPressed: _removeSelectedUsers,
              text: 'Удалить исполнителей',
            ),
          )
        ],
      ),
    ]);
  }
}
